﻿using Codeless.Framework.DependencyInjection;
using Codeless.Server.Common.Enumerations;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using System.Web;

namespace Codeless.Server.Common
{
    public class TokenHelper : ITokenHelper
    {
        private IConfigSettings configSettings;

        private readonly ConcurrentDictionary<(string, Token, string), string> resultCache = new ConcurrentDictionary<(string, Token, string), string>();
        private Dictionary<Token, Func<string>> tokenReplacements = new Dictionary<Token, Func<string>>();

        public string Replace(string initialString, Token token, string overwriteTokenValue = null)
        {
            var key = (initialString.ToLowerInvariant(), token, overwriteTokenValue);

            // Check if in cache
            if (resultCache.TryGetValue(key, out string cachedString))
            {
                return cachedString;
            }

            string result = ReplaceNonCached(initialString, token, overwriteTokenValue);
            resultCache.TryAdd(key, result);
            return result;
        }

        private string ReplaceNonCached(string initialString, Token token, string overwriteTokenValue = null)
        {
            string result = initialString;

            if (token == Token.ExternalQueueConnection)
            {
                var matches = Regex.Matches(initialString, @"\[(.*?)\]", RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(configSettings.RegExTimeoutMs));
                foreach (Match match in matches)
                {
                    if (match.Groups.Count >= 2)
                    {
                        string connectionName = match.Groups[1].Value;
                        result = ReplaceToken(result, connectionName, configSettings.GetExternalQueueConnection(connectionName));
                    }
                }
            }
            else if (token == Token.BaseUrlRegex)
            {
                string baseUrl = ResolveTokenValue(configSettings.BaseUrl, overwriteTokenValue);
                result = ReplaceFilterToken(initialString, Token.BaseUrlRegex, baseUrl);
            }
            else if (token == Token.ApplicationName)
            {
                if (string.IsNullOrWhiteSpace(configSettings.ApplicationName))
                {
                    throw new InvalidOperationException("The application name setting must be filled.");
                }
                result = ReplaceToken(initialString, Token.ApplicationName.GetStringValue(), configSettings.ApplicationName);
            }
            else if (token == Token.CustomConfig)
            {
                result = configSettings.ReplaceCustomConfigTokens(result);
            }
            else if (token == Token.ArtifactsLocation)
            {
                string replacement = ResolveTokenValue(string.Empty, overwriteTokenValue);
                result = ReplaceToken(initialString, Token.ArtifactsLocation.GetStringValue(), replacement);
            }
            else if (tokenReplacements.TryGetValue(token, out var replacementFunc))
            {
                result = ReplaceToken(initialString, token.GetStringValue(), replacementFunc());
            }

            return result;
        }

        private string ReplaceToken(string input, string token, string replacement)
        {
            return Regex.Replace(input, $@"\[{token.Trim('[', ']')}\]", replacement, RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(this.configSettings.RegExTimeoutMs));
        }


        public TokenHelper()
        {

        }



        [InjectionMethod]
        public void Initialize(IConfigSettings configSettings)
        {
            this.configSettings = configSettings ?? throw new ArgumentNullException(nameof(configSettings));
            this.tokenReplacements = new Dictionary<Token, Func<string>>
                                {
                                    { Token.ServerBaseUrl, () => configSettings.BaseUrl },
                                    { Token.ServerFiles, () => configSettings.ServerFiles.TrimEnd('\\') },
                                    { Token.ModelFiles, () => configSettings.ModelFilesPath.TrimEnd('\\') },
                                    { Token.EnvironmentName, () => configSettings.EnvironmentName.TrimEnd('/') },
                                    { Token.RootUrl, () => configSettings.RootUrl.TrimEnd('/') },
                                    { Token.BaseUrl, () => configSettings.BaseUrl.TrimEnd('/') },
                                    { Token.ServerFilesHtml, () => HttpUtility.HtmlEncode(configSettings.ServerFiles.TrimEnd('\\').TrimEnd('/')) },
                                    { Token.RabbitMqUrl, () => configSettings.RabbitMqUrl.TrimEnd('/') },
                                    { Token.PackageGuid, () => configSettings.PackageGuid },
                                    { Token.PackageName, () => configSettings.PackageName },
                                    { Token.PackageDescription, () => configSettings.PackageDescription },
                                    { Token.SmtpDomain, () => configSettings.SmtpDomain.TrimEnd('/') },
                                    { Token.SmtpPassword, () => configSettings.SmtpPassword.TrimEnd('/') },
                                    { Token.SmtpPort, () => configSettings.SmtpPort.ToString() },
                                    { Token.SmtpServer, () => configSettings.SmtpServer.TrimEnd('/') },
                                    { Token.SmtpUser, () => configSettings.SmtpUser.TrimEnd('/') },
                                    { Token.SmtpUseSsl, () => configSettings.SmtpUseSsl.ToString() },
                                    { Token.RootName, () => configSettings.RootName.TrimEnd('/') },
                                    { Token.TaskMonitorEnabled, () => configSettings.TaskMonitorEnabled.ToString() },
                                    { Token.EnvironmentType, () => configSettings.EnvironmentType },
                                    { Token.IsDevelopmentEnvironment, () => configSettings.IsDevelopmentEnvironment },
                                    { Token.UnifiedApplicationName, () => configSettings.UnifiedApplicationName },
                                };
        }

        public string ReplaceFilterToken(string initialString, Token token, string replacement)
        {
            string result = initialString;

            switch (token)
            {
                case Token.CurrentUser:
                    result = Regex.Replace(initialString, Regex.Escape(Token.CurrentUser.GetStringValue()), replacement, RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(configSettings.RegExTimeoutMs));
                    break;

                case Token.BaseUrlRegex:
                    result = Regex.Replace(initialString, Token.BaseUrlRegex.GetStringValue(), replacement, RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(configSettings.RegExTimeoutMs));
                    break;

                default:
                    // No replacement needed, keep the initial string
                    break;
            }

            return result;
        }



        private static string ResolveTokenValue(string configValue, string overwriteTokenValue)
        {
            if (overwriteTokenValue != null)
            {
                return overwriteTokenValue;
            }
            return configValue;
        }

        #region Singleton

        public static ITokenHelper Instance
        {
            get
            {
                return Nested.instance;
            }
        }

        class Nested
        {
            protected Nested()
            { }

            // Explicit static constructor to tell C# compiler
            // not to mark type as beforefieldinit
            static Nested()
            {
            }

            internal static readonly ITokenHelper instance = new TokenHelper();
        }

        #endregion
    }
}



