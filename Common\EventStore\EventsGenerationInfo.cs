﻿using System;

namespace Codeless.Server.Common.EventStore
{
    public class EventsGenerationInfo
    {
        public EventsGenerationInfo(EventsGenerationRequest request, EventsGenerationRequestStatus status)
        {
            this.Id = request.Id;
            this.EventsType = request.EventsType;
            this.Count = request.Count;
            this.Failed = request.Failed;
            this.IntervalMin = request.IntervalMin;
            this.IntervalMax = request.IntervalMax;
            this.Status = status;
        }

        public string RequestStatus
        {
            get { return Status.ToString(); }
        }

        public string RequestedEventsType
        {
            get { return EventsType.ToString(); }
        }

        public EventsGenerationRequestStatus Status { get; set; }
        public Guid Id { get; private set; }
        public int Count { get; private set; }
        public int IntervalMin { get; private set; }
        public int IntervalMax { get; private set; }
        public EventsGenerationEventType EventsType { get; private set; }
        public bool Failed { get; set; }
        public DateTime? StartGeneration { get; set; }
        public DateTime? EndGeneration { get; set; }
    }
}