﻿using Codeless.Server.Interfaces;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Codeless.Server.Common.Cache.DataVersions
{
    public interface IDataVersionCache : IBackgroundWorker
    {
        event DataVersionCache.VersionChangedHandler VersionChanged;

        Dictionary<int, DataVersion> GetVersions(string concern, IEnumerable<int> classIds, bool assignConcern = false);
        void IncreaseVersion(string concern, int classId, DataVersion fixedDataVersion = null);
        void IncreaseVersions(string concern, IEnumerable<int> classIds);
        void Start();
        List<Task> Stop();
        DataVersionUpdaterInfo GetDataVersionsUpdaterInfo();
    }
}