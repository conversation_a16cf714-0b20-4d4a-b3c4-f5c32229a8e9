﻿using Codeless.Framework.DependencyInjection;
using Codeless.Framework.IO.Standard;
using Codeless.Server.Common.Cache.CacheVersions;
using Codeless.Server.Common.ConnectionInformation;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;

namespace Codeless.Server.Common.FunctionsByApplication
{
    public class FunctionsByApplicationProvider : IFunctionsByApplicationProvider
    {
        private IConfigSettings configSettings;
        private IPackageInfo packageInfo;
        private IFileSystem fileSystem;
        private ICacheVersionsCache cacheVersionsCache;
        private string modelVersion;

        private const string FunctionsJson = "Functions.json";
        private ConcurrentDictionary<int, FunctionInfo> functionsByApplicationCache;

        public FunctionInfo GetFunctionById(int functionId)
        {
            EnsureCorrectDataLoaded();

            if (functionsByApplicationCache.TryGetValue(functionId, out var functionInfo))
            {
                return functionInfo;
            }

            return null;
        }

        public void LoadFunctionsByApplication()
        {
            functionsByApplicationCache = new ConcurrentDictionary<int, FunctionInfo>();

            if (configSettings.IsUnifiedClient)
            {
                var artifactsDirectory = packageInfo.GetDefaultPackage().ServerFiles.UnifiedArtifactsCurrentFolder;
                var filePath = PathExt.Combine(artifactsDirectory, FunctionsJson);

                if (fileSystem.FileExists(filePath))
                {
                    var functionsText = fileSystem.ReadAllText(filePath);

                    var functions = JsonSerializer.Deserialize<List<FunctionInfo>>(functionsText);
                    functions?.ForEach(item => functionsByApplicationCache.TryAdd(item.FunctionId, item));
                }
                else
                {
                    throw new FileNotFoundException(filePath);
                }
            }

            modelVersion = cacheVersionsCache.GetMetadataVersion().ToShortFormat();
        }

        private void EnsureCorrectDataLoaded()
        {
            if (configSettings.EventStore != null && configSettings.EventStore.Enabled && configSettings.UseEventStoreCacheVersions)
            {
                return;
            }

            string currentModelVersion = cacheVersionsCache.GetMetadataVersion().ToShortFormat();
            bool needsUpdate = (functionsByApplicationCache != null && modelVersion != currentModelVersion) || functionsByApplicationCache == null;

            if (needsUpdate)
            {
                LoadFunctionsByApplication();
            }
        }

        [InjectionMethod]
        public void Initialize(IConfigSettings configSettings, IFileSystem fileSystem, IPackageInfo packageInfo,
            ICacheVersionsCache cacheVersionsCache)
        {
            this.configSettings = configSettings ?? throw new ArgumentNullException(nameof(configSettings));
            this.packageInfo = packageInfo ?? throw new ArgumentNullException(nameof(packageInfo));
            this.fileSystem = fileSystem ?? throw new ArgumentNullException(nameof(fileSystem));
            this.cacheVersionsCache = cacheVersionsCache ?? throw new ArgumentNullException(nameof(cacheVersionsCache));

            LoadFunctionsByApplication();
        }

        #region Singleton

        public static IFunctionsByApplicationProvider Instance
        {
            get
            {
                return Nested.instance;
            }
        }

        class Nested
        {
            protected Nested()
            { }
            static Nested()
            { }

            internal static readonly IFunctionsByApplicationProvider instance = new FunctionsByApplicationProvider();
        }

        #endregion
    }
}
