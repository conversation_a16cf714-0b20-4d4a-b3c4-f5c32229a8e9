﻿using Codeless.Common;
using Codeless.Common.RuntimeDataAccess;
using Codeless.Framework.DependencyInjection;
using Codeless.Server.Common.Cache.CacheVersions;
using Codeless.Server.Common.ClassesByApplication;
using Codeless.Server.Common.ConnectionInformation;
using Codeless.Server.Common.EventStore;
using Codeless.Server.Common.FunctionsByApplication;
using Codeless.Server.Common.Repository;
using Codeless.Server.Common.RuntimeClasses;
using Codeless.Server.Common.SystemSettingsClasses;
using Codeless.Server.Interfaces.Common.Cache;
using Codeless.Server.MetaClasses;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Codeless.Server.Common.Helper
{
    public class CacheVersionUtilities : ICacheVersionUtilities
    {
        private ICacheVersionsCache cacheVersionsCache;
        private IConfigSettings configSettings;
        private IPackageInfo packageInfo;
        private IRuntimeClassesProvider runtimeClassesProvider;
        private ISystemSettingsClassesProvider systemSettingsClassesProvider;
        private IClassesByApplicationProvider classesByApplicationProvider;
        private IFunctionsByApplicationProvider functionsByApplicationProvider;
        private Codeless.Server.MetaClassesXML.IMetaClassesCache metaClassesCache;
        private IRuntimeDataProvider runtimeDataProvider;
        private IScreenMetadataRepository screenMetadataRepository;
        private IClassMetadataRepository classMetadataRepository;
        private IMetaData metaData;

        public void HandleCacheVersionIncreased(int version)
        {
            UpdateListSettingsCache();

            string newVersionString = version.ToString().PadLeft(9, '0');
            ClearMetaDataCache(new MetadataVersion(newVersionString));
        }

        public void UpdateListSettingsCache()
        {
            if (!configSettings.RuntimeDataModelAndApplicationLists)
            {
                cacheVersionsCache.UpdateCacheVersion(CacheVersionTypes.ListSettings, null, false);
            }
        }

        public void ClearMetaDataCache(MetadataVersion newVersion)
        {
            metaClassesCache.ClearCache();
            runtimeDataProvider.ClearCache();

            UpdatePackage(newVersion);
        }

        private void UpdatePackage(MetadataVersion newVersion)
        {
            var package = packageInfo.GetDefaultPackage();

            package.SetModelVersion(newVersion);
            package.ReloadServerFiles();

            MetadataVersion newMetadataVersion = cacheVersionsCache.GetMetadataVersion(true);
            var invalidClasses = metaData.GetCachedPackage().ResetCacheVersion(newMetadataVersion);

            LoadClassesOnSeparateThreadIfNeeded(invalidClasses);

            if (configSettings.ArtifactsUseForPackageUpdater)
            {
                screenMetadataRepository.ClearCache();
                classMetadataRepository.ClearCache();

                ReloadRuntimeAndApplicationData();

                EventStoreManagerSingleton.Instance.GetModelContainer().OnModelVersionUpdated();
            }
        }

        private void ReloadRuntimeAndApplicationData()
        {
            classesByApplicationProvider.LoadClassesByApplication();
            functionsByApplicationProvider.LoadFunctionsByApplication();
            runtimeClassesProvider.LoadRuntimeClasses();
            systemSettingsClassesProvider.LoadSystemSettingsClasses();
        }

        private void LoadClassesOnSeparateThreadIfNeeded(List<int> invalidClasses)
        {
            if (invalidClasses == null || invalidClasses.Count == 0)
            {
                return;
            }

            Task.Factory.StartNew(() =>
            {
                ParallelLimited.ForEach(invalidClasses, (int classId) =>
                {
                    metaData.Package.GetClass(classId, true, false, true);
                });

                FlushRelatedClasses(invalidClasses);
            });
        }

        private void FlushRelatedClasses(List<int> classIds)
        {
            var invalidClasses = new List<int>();

            foreach (var classId in classIds)
            {
                var metaClass = metaData.Package.GetClass(classId);

                invalidClasses.AddRangeUnique(metaClass.Associations.OfType<cAssociation>().Select(x => x.ClassIDTo));
                invalidClasses.AddRangeUnique(metaClass.Functions.OfType<cFunction>().SelectMany(x => x.NavigationsFrom.OfType<cNavigation>().Select(y => y.ClassIDTo)));
            }

            metaData.FlushClasses(invalidClasses);
        }

        [InjectionMethod]
        public void Initialize(IDependencyResolver dependencyResolver)
        {
            this.configSettings = dependencyResolver.Resolve<IConfigSettings>();
            this.cacheVersionsCache = dependencyResolver.Resolve<ICacheVersionsCache>();
            this.packageInfo = dependencyResolver.Resolve<IPackageInfo>();
            this.runtimeClassesProvider = dependencyResolver.Resolve<IRuntimeClassesProvider>();
            this.systemSettingsClassesProvider = dependencyResolver.Resolve<ISystemSettingsClassesProvider>();
            this.classesByApplicationProvider = dependencyResolver.Resolve<IClassesByApplicationProvider>();
            this.functionsByApplicationProvider = dependencyResolver.Resolve<IFunctionsByApplicationProvider>();
            this.metaClassesCache = dependencyResolver.Resolve<Codeless.Server.MetaClassesXML.IMetaClassesCache>();
            this.metaData = dependencyResolver.Resolve<IMetaData>();
            this.runtimeDataProvider = dependencyResolver.Resolve<IRuntimeDataProvider>();
            this.screenMetadataRepository = dependencyResolver.Resolve<IScreenMetadataRepository>();
            this.classMetadataRepository = dependencyResolver.Resolve<IClassMetadataRepository>();
        }

        #region Singleton

        public static ICacheVersionUtilities Instance { get => Nested.instance; }

        class Nested
        {
            protected Nested() { }
            static Nested() { }

            internal static readonly ICacheVersionUtilities instance = new CacheVersionUtilities();
        }

        #endregion
    }
}
