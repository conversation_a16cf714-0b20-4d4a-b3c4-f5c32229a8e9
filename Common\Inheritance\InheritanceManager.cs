﻿using Codeless.Common.RuntimeDataAccess;
using Codeless.Server.Common.Cache.Company;
using Codeless.Server.Common.Cache.DataVersions;
using Codeless.Server.Interfaces.RuntimeDataAccess.Dto;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Codeless.Server.Common.Inheritance
{
    public class InheritanceManager
    {
        private readonly IRuntimeDataProvider runtimeDataProvider;
        private readonly IDataVersionCache dataVersionCache;
        private readonly IRununitsAndCompaniesCache rununitsAndCompaniesCache;

        private Dictionary<int, RuntimeClass> inheritedClasses;

        public InheritanceManager(IRuntimeDataProvider runtimeDataProvider, IDataVersionCache dataVersionCache, IRununitsAndCompaniesCache rununitsAndCompaniesCache)
        {
            this.runtimeDataProvider = runtimeDataProvider ?? throw new ArgumentNullException(nameof(runtimeDataProvider));
            this.dataVersionCache = dataVersionCache ?? throw new ArgumentNullException(nameof(dataVersionCache));
            this.rununitsAndCompaniesCache = rununitsAndCompaniesCache ?? throw new ArgumentNullException(nameof(rununitsAndCompaniesCache));
        }

        /// <returns>The service return all class that are inherited classes with read-only set that are linked to the requested storage connection(s). For each class found, also the data version number will be added to the result. </returns>
        public List<Inheritance> GetInheritedClassesByConnectionName(string storageConnectionList)
        {
            var storageConnections = SplitStorageConnections(storageConnectionList);

            List<RuntimeClass> classes = this.runtimeDataProvider.GetInheritedClasses();
            inheritedClasses = classes.ToDictionary(x => x.ClassId);

            return GetByStorageConnection(storageConnections);
        }

        private static string[] SplitStorageConnections(string storageConnectionList)
        {
            if (string.IsNullOrEmpty(storageConnectionList))
            {
                throw new ArgumentNullException(nameof(storageConnectionList));
            }

            var storageConnections = storageConnectionList.Split(",".ToCharArray(), StringSplitOptions.RemoveEmptyEntries);

            for (int i = 0; i < storageConnections.Length; i++)
            {
                storageConnections[i] = storageConnections[i].Trim();
            }

            return storageConnections;
        }

        private List<Inheritance> GetByStorageConnection(string[] storageConnections)
        {
            Dictionary<(string, int), Inheritance> inheritances = ClassesWithInheritances(storageConnections);

            if (inheritances.Any())
            {
                IEnumerable<int> classIds = inheritances.Keys.Select(k => k.Item2);
                List<string> rununits = rununitsAndCompaniesCache.GetConcerns();
                rununits.Insert(0, Common.Constants.AllConcernCode);

                rununits.ForEach(rununit =>
                {
                    AddOrUpdateDataVersions(inheritances, classIds, rununit);
                });
            }

            return inheritances.Values.ToList();
        }

        private void AddOrUpdateDataVersions(Dictionary<(string, int), Inheritance> inheritances, IEnumerable<int> classIds, string rununit)
        {
            Dictionary<int, DataVersion> versions = dataVersionCache.GetVersions(rununit, classIds, true);

            if (versions.Any())
            {
                foreach (var item in versions)
                {
                    if (item.Value.Concern is null) continue;

                    if (inheritances.TryGetValue((item.Value.Concern, item.Key), out Inheritance dataVersion))
                    {
                        if (item.Value.Version > dataVersion.DataVersion)
                            dataVersion.DataVersion = item.Value.Version;
                    }
                    else if (inheritedClasses.TryGetValue(item.Key, out RuntimeClass runtimeClass))
                    {
                        inheritances.Add((item.Value.Concern, item.Key), new Inheritance
                        {
                            BaseClassId = runtimeClass.BaseClassId,
                            ClassId = runtimeClass.ClassId,
                            ConnectionName = runtimeClass.ConnectionName,
                            ConcernCode = item.Value.Concern,
                            DataVersion = item.Value.Version
                        });
                    }
                }
            }
        }

        private Dictionary<(string, int), Inheritance> ClassesWithInheritances(string[] storageConnections)
        {
            var result = new Dictionary<(string, int), Inheritance>();

            foreach (var inheritedClass in inheritedClasses.Values)
            {
                if (storageConnections.Contains(inheritedClass.ConnectionName))
                {
                    AddInheritance(result, inheritedClass);
                }
            }

            return result;
        }

        private void AddInheritance(Dictionary<(string, int), Inheritance> inheritancesList, RuntimeClass inheritedClass)
        {
            inheritancesList.Add((Common.Constants.AllConcernCode, inheritedClass.ClassId), new Inheritance
            {
                ClassId = inheritedClass.ClassId,
                BaseClassId = inheritedClass.BaseClassId,
                ConnectionName = inheritedClass.ConnectionName,
                ConcernCode = Common.Constants.AllConcernCode
            });
        }
    }
}
