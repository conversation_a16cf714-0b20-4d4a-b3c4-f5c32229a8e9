﻿using System;

namespace Codeless.Server.Common.Cache.CacheBaseClasses
{
    /// <summary>
    /// Cache item for generic ObjectCache.
    /// </summary>
    /// <typeparam name="ContentType"></typeparam>
    /// <typeparam name="KeyType"></typeparam>
    public class ObjectCacheItem<ContentType, KeyType> where ContentType : class
    {
        public ObjectCacheItem<ContentType, KeyType> NextCacheItem { get; set; }
        public ObjectCacheItem<ContentType, KeyType> PreviousCacheItem { get; set; }
        public ContentType Content { get; set; }
        public KeyType Key { get; set; }
        public int Size { get; set; }
        public DateTime LastAcccessed { get; set; }

        public ObjectCacheItem(ContentType content, KeyType key, int size)
        {
            this.Content = content;
            this.Key = key;
            this.Size = size;
            this.LastAcccessed = DateTime.Now;
        }
    }
}