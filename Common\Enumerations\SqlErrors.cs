﻿namespace Codeless.Server.Common.Enumerations
{
    /// <summary>
    /// SQL System Error Messages - https://technet.microsoft.com/en-us/library/cc645603
    /// </summary>
    public enum SqlErrors
    {
        TimeoutExpired = -2,
        ServerNotFound = 17,
        IncorrectSyntax = 102,
        IncorrectInsertStatementMoreColumns = 109,
        IncorrectInsertStatementFewerColumns = 110,
        IncorrectSyntaxNearKeyword = 156,
        IncorrectSyntax2 = 170,
        InvalidColumnName = 207,
        InvalidObjectName = 208,
        PermissionDeniedOnObject = 229,
        MoneyOverflowError = 236,
        SortTextNotSupported = 306,
        IncompatibleEqualOperatorTextField = 402,
        CannotInsertNullInColumn = 515,
        ColumnOverflow = 517,
        SingleUserMode = 924,
        TransactionDeadlocked = 1205,
        DistributedTransactionCancelled = 1206,
        LockRequestTimeout = 1222,
        CouldNotObtainExclusiveLock = 1807,
        CouldNotInsertDuplicateIndex = 2601,
        CouldNotInsertDuplicateKey = 2627,
        MissingStoredProcedure = 2816,
        ArithmeticOverflow = 3606,
        CannotOpenDatabaseLoginFailed = 4060,
        ColumnNamesMustBeUnique = 4506,
        ArithmeticOverflowConvertingToDataType = 8115,
        LoginFailed = 18456,
        LicensesExceeded = 18458
    }

    public enum MongoErrors
    {
        CouldNotInsertDuplicateKey = 11000,
        LockRequestTimeout = 0
    }
}