﻿namespace Codeless.Server.Common.Enumerations
{
    public enum FunctionIconFileFormat
    {
        PNG = 0,
        SVG = 1
    }

    public static class FunctionIconFileExtension
    {
        public const string PNG = "png";
        public const string SVG = "svg";
    }

    public static class FunctionIconDefaultMappings
    {
        public const int FolderClosedIconId = 1420;
        public const FunctionIconFileFormat FolderClosedIconFileFormat = FunctionIconFileFormat.SVG;

        public const int FolderOpenIconId = 1403;
        public const FunctionIconFileFormat FolderOpenIconFileFormat = FunctionIconFileFormat.SVG;

        public const int MoreIconId = 1416;
        public const FunctionIconFileFormat MoreIconFileFormat = FunctionIconFileFormat.SVG;

        public const int RegistrationFunctionIconId = 1401;
        public const FunctionIconFileFormat RegistrationFunctionIconFileFormat = FunctionIconFileFormat.SVG;

        public const int ProcessFunctionIconId = 1402;
        public const FunctionIconFileFormat ProcessFunctionIconFileFormat = FunctionIconFileFormat.SVG;

        public const int AddFunctionIconId = 1513;
        public const FunctionIconFileFormat AddFunctionIconFileFormat = FunctionIconFileFormat.SVG;

        public const int SuggestionsIconId = 1595;
        public const FunctionIconFileFormat SuggestionsIconFileFormat = FunctionIconFileFormat.SVG;

        public const int FunctionSearchResultsIconId = 1413;
        public const FunctionIconFileFormat FunctionSearchResultsFileFormat = FunctionIconFileFormat.SVG;
    }
}