﻿using Codeless.Framework.EventProcessing.DTO;
using Codeless.Framework.EventProcessing.MongoDB.DTO;
using System.Collections.Concurrent;

namespace Codeless.Server.Common.Cache.ClassStateCache
{
	public class ClassStateCache : IClassStateCache
	{
		private readonly ConcurrentDictionary<(int classId, string concern), ClassStateModel> notAvailableClasses = new ConcurrentDictionary<(int classId, string concern), ClassStateModel>();

		public ClassStateModel GetClassState(int classId, string concern)
		{
			if (notAvailableClasses.TryGetValue((classId, concern), out ClassStateModel classState))
			{
				return classState;
			}

			return null;
		}

		public void UpdateClassState(ClassStateModel classState)
		{
			if (classState == null)
			{
				return;
			}

			if (classState.State == ClassState.Available && this.notAvailableClasses.ContainsKey((classState.ClassId, classState.Concern)))
			{
				notAvailableClasses.TryRemove((classState.ClassId, classState.Concern), out _);
			}
			else if (classState.State != ClassState.Available)
			{
				notAvailableClasses.AddOrUpdate((classState.ClassId, classState.Concern), classState, (key, oldValue) => classState);
			}
		}

		#region Singleton

		public static IClassStateCache Instance
		{
			get
			{
				return Nested.instance;
			}
		}

		class Nested
		{
			protected Nested()
			{
			}

			static Nested()
			{
			}

			internal static readonly IClassStateCache instance = new ClassStateCache();
		}

		#endregion
	}
}
