﻿using System.Collections.Generic;

namespace Codeless.Server.Common.Cache.CacheBaseClasses
{
    public abstract class DictionaryCacheBaseWithParameter<TKey, TValue, TParameter> : CacheWithParameter<TParameter>
    {
        protected readonly Dictionary<TKey, TValue> items = new Dictionary<TKey, TValue>();

        protected virtual TValue GetCacheItem(TKey key, TParameter parameter)
        {
            EnsureCorrectDataLoaded(parameter);

            TValue item = default(TValue);
            bool inCache = false;
            this.lockManager.LockReadMode(() =>
            {
                inCache = this.items.TryGetValue(key, out item);
            });

            return item;
        }

        protected virtual bool TryGetCacheItem(TKey key, TParameter parameter, out TValue item)
        {
            EnsureCorrectDataLoaded(parameter);

            bool inCache = false;
            item = this.lockManager.LockReadMode(() =>
            {
                TValue foundItem;
                inCache = this.items.TryGetValue(key, out foundItem);
                return foundItem;
            });

            return inCache;
        }

        public override void Flush()
        {
            this.lockManager.LockWriteMode(() =>
            {
                this.items.Clear();
                this.loadedVersion = null;
            });
        }
    }
}
