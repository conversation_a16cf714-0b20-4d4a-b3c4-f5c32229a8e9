﻿using Codeless.Common;
using Codeless.Framework.LockManagement;
using Codeless.Framework.Logging.Standard;
using Codeless.Framework.Sql.Standard;
using Codeless.Server.Common.ConnectionInformation;
using Codeless.Server.Common.Enumerations;
using Codeless.Server.Common.Repository;
using Codeless.Server.Interfaces.Common.Cache;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Reflection;

namespace Codeless.Server.Common.Cache.CacheVersions
{
    public abstract class CacheVersionsBase : ICacheVersionsCache
    {
        protected readonly VersionInfo Versions = new VersionInfo();
        protected readonly Dictionary<string, VersionInfo> UserVersions = new Dictionary<string, VersionInfo>();

        protected readonly Dictionary<string, ILockManager> lockManagersPersonal = new Dictionary<string, ILockManager>();
        protected ILogger logger;
        protected ILockManagerFactory lockManagerFactory;
        protected ISqlWorkerFactory sqlWorkerFactory;
        protected IConfigSettings configSettings;
        protected ISqlConnectionManager sqlConnectionManager;
        protected ILockManager lockManagerGlobal;
        protected ILockManager lockManagerPersonalList;
        protected IMetadataVersionRepository metadataVersionRepository;
        
        protected MetadataVersion MetadataVersion;
        protected DateTime LastMetadataLoadTime;
        protected int reloadInterval;
        protected int reloadMetadataInterval;

        private readonly Dictionary<CacheVersionTypes, Func<int>> cacheGetVersionFunctions = new Dictionary<CacheVersionTypes, Func<int>>();
        private readonly Dictionary<CacheVersionTypes, Action<VersionInfo, int>> cacheAssignVersionActions = new Dictionary<CacheVersionTypes, Action<VersionInfo, int>>();
        protected readonly Dictionary<CacheVersionTypes, Func<VersionInfo, int>> PersonalCacheversionsSearch = new Dictionary<CacheVersionTypes, Func<VersionInfo, int>>();

        public CacheVersionsBase()
        {
            InitializePersonalCacheVersionSearchDictionary();
            InitializeCacheGetVersionFunctionsDictionary();
            InitializeCacheAssignVersionActionsDictionary();
        }

        public abstract int GetCacheVersion(CacheVersionTypes cacheVersionType, bool forceReload = false);

        public abstract int GetCacheVersionPersonal(CacheVersionTypes cacheVersionType, string userId, bool forceReload = false);

        public abstract VersionInfo GetCacheVersions(bool forceReload = false);

        public abstract VersionInfo GetCacheVersionsPersonal(string userId, bool forceReload = false);

        public abstract void UpdateCacheVersion(CacheVersionTypes type, int? version, bool runtimeModelUpdate);

        public abstract void UpdateCacheVersionPersonal(string userId, CacheVersionTypes type, int? version, bool runtimeModelUpdate);


        private readonly Dictionary<CacheVersionTypes, List<Action<int>>> cacheVersionChangeActions = new Dictionary<CacheVersionTypes, List<Action<int>>>();

        public void RegisterOnCacheVersionChange(CacheVersionTypes type, Action<int> actionOnVersionChange)
        {
            lockManagerGlobal.LockWriteMode(() =>
            {
                if (!cacheVersionChangeActions.ContainsKey(type))
                {
                    cacheVersionChangeActions.Add(type, new List<Action<int>>());
                }
                List<Action<int>> actions = cacheVersionChangeActions[type];
                actions.Add(actionOnVersionChange);
            });
        }

        private void OnCacheVersionChange(CacheVersionTypes type, int? version)
        {
            if (version.HasValue)
            {
                lockManagerGlobal.LockReadMode(() =>
                {
                    if (cacheVersionChangeActions.TryGetValue(type, out List<Action<int>> actions))
                    {
                        foreach (Action<int> action in actions)
                        {
                            action.Invoke(version.Value);
                        }
                    }
                });
            }
        }

        public VersionInfo GetCacheVersionsFromDatabase()
        {
            VersionInfo result = new VersionInfo();
            GetCacheVersionsFromDatabase(ref result);
            return result;

        }

        public VersionInfo GetCacheVersionsFromDatabase(VersionInfo resultVersion)
        {
            GetCacheVersionsFromDatabase(ref resultVersion);
            return resultVersion;
        }

        private void GetCacheVersionsFromDatabase(ref VersionInfo resultVersion)
        {
            using (IDbConnection connection = sqlConnectionManager.GetNewOpenedSqlConnection(Constants.ALL, false))
            {
                ISqlWorker sqlWorker = sqlWorkerFactory.GetSqlWorker(connection);

                List<CacheVersionDto> cacheVersionList = sqlWorker.ExecuteQuery<CacheVersionDto>(this.queryGetCacheVersions).ToList();
                foreach (CacheVersionDto cacheVersion in cacheVersionList)
                {
                    FillVersionInfo(resultVersion, cacheVersion);
                }
            }

            resultVersion.LoadTime = DateTime.Now;
        }

        public MetadataVersion GetMetadataVersion(bool forceReload = false)
        {
            EnsureCorrectMetadataVersion(forceReload);

            MetadataVersion result = lockManagerGlobal.LockReadMode(() =>
            {
                return this.MetadataVersion;
            });

            return result;
        }

        private readonly static string lockManagerNameGlobal = $"{typeof(CacheVersionsCache).FullName}_Global";
        private readonly static string lockManagerNamePersonal = $"{typeof(CacheVersionsCache).FullName}_GlobalPersonaList";

        protected void InitializeCache(ILoggerFactory loggerFactory, ILockManagerFactory lockManagerFactory, IPackageInfo packageInfo, IConfigSettings configSettings, ISqlWorkerFactory sqlWorkerFactory, ISqlConnectionManager sqlConnectionManager)
        {
            logger = loggerFactory?.GetLogger(typeof(CacheVersionsCache)) ?? throw new ArgumentNullException(nameof(loggerFactory));
            this.lockManagerFactory = lockManagerFactory ?? throw new ArgumentNullException(nameof(lockManagerFactory));
            this.sqlWorkerFactory = sqlWorkerFactory ?? throw new ArgumentNullException(nameof(sqlWorkerFactory));
            this.configSettings = configSettings ?? throw new ArgumentNullException(nameof(configSettings));
            packageInfo = packageInfo ?? throw new ArgumentNullException(nameof(packageInfo));
            this.sqlConnectionManager = sqlConnectionManager ?? throw new ArgumentNullException(nameof(sqlConnectionManager));

            this.lockManagerGlobal = lockManagerFactory.GetLockManager(lockManagerNameGlobal);
            this.lockManagerPersonalList = lockManagerFactory.GetLockManager(lockManagerNamePersonal);

            this.reloadInterval = configSettings.CacheVersionCheckIntervalRuntime;
            this.reloadMetadataInterval = configSettings.CacheVersionCheckIntervalMetadata;

            this.metadataVersionRepository = MetadataVersionRepository.Instance;

            try
            {
                packageInfo.GetDefaultPackage().ServerBuildVersion = Assembly.GetExecutingAssembly().GetName().Version.ToString();
            }
            catch (Exception ex)
            {
                logger.WriteError("Failed to update T000Settings \"Version\" (ServerBuildVersion).", ex);
            }
        }

        protected ILockManager GetPersonalLockManager(string userId)
        {
            ILockManager lockManager;

            bool inCache = false;

            string key = userId.ToLower();

            lockManager = lockManagerPersonalList.LockReadMode(() =>
            {
                inCache = this.lockManagersPersonal.TryGetValue(key, out lockManager);
                return lockManager;
            });

            if (!inCache)
            {
                lockManager = lockManagerPersonalList.LockWriteMode(() =>
                {
                    if (!this.lockManagersPersonal.TryGetValue(key, out lockManager))
                    {
                        string lockName = $"{typeof(CacheVersionsCache)}_Personal_{userId}";
                        lockManager = lockManagerFactory.GetLockManager(lockName);
                        this.lockManagersPersonal.Add(key, lockManager);
                    }

                    return lockManager;
                });
            }

            return lockManager;
        }

        protected VersionInfo GetVersionsPersonalFromDatabase(string userId)
        {
            VersionInfo result = new VersionInfo();

            using (IDbConnection connection = sqlConnectionManager.GetNewOpenedSqlConnection(Constants.ALL, false))
            {
                ISqlWorker sqlWorker = sqlWorkerFactory.GetSqlWorker(connection);

                IEnumerable<CacheVersionDto> cacheVersionList = sqlWorker.ExecuteQuery<CacheVersionDto>(this.queryGetCacheVersionsPersonal, new SqlParameter("@UserId", userId)).ToList();
                foreach (CacheVersionDto cacheVersion in cacheVersionList)
                {
                    FillVersionInfo(result, cacheVersion);
                }
            }

            result.LoadTime = DateTime.Now;

            return result;
        }

        protected void HandleFailedInsert(SqlException sqlException, ref int numberOfAttempts)
        {
            if (sqlException.Number == (int)SqlErrors.CouldNotInsertDuplicateKey)
            {
                numberOfAttempts++;

                if (numberOfAttempts == this.configSettings.SqlMaxNumberOfInsertAttempts)
                {
                    throw sqlException;
                }
            }
            else
            {
                throw sqlException;
            }
        }

        public void SetCacheVersionInDatabase(CacheVersionTypes type, int version)
        {
            SetCacheVersionInDatabase(type, (int?)version);
        }

        protected void UpdateCacheVersionInDatabase(CacheVersionTypes type)
        {
            SetCacheVersionInDatabase(type, null);
        }

        private void SetCacheVersionInDatabase(CacheVersionTypes type, int? version)
        {
            int i = 0;
            bool inserted = false;
            decimal objectId = 0;

            using (IDbConnection connection = sqlConnectionManager.GetNewOpenedSqlConnection(Constants.ALL, false))
            {
                ISqlWorker sqlWorker = sqlWorkerFactory.GetSqlWorker(connection);

                while (!inserted && i < this.configSettings.SqlMaxNumberOfInsertAttempts)
                {
                    try
                    {
                        objectId = RandomIdGenerator.Instance.GetRandomLong();

                        string sql = string.Format(queryUpdateCacheVersion, version.HasValue ? querySet : queryIncrement);

                        List<SqlParameter> parameters = new List<SqlParameter>()
                        {
                            new SqlParameter("ObjectId", objectId),
                            new SqlParameter("Type", (int)type)
                        };

                        if (version.HasValue)
                        {
                            parameters.Add(new SqlParameter("Version", version.Value));
                        }

                        sqlWorker.ExecuteCommand(sql, parameters.ToArray());

                        inserted = true;
                    }
                    catch (SqlException ex)
                    {
                        if (ex.Number == (int)SqlErrors.ArithmeticOverflowConvertingToDataType)
                        {
                            sqlWorker.ExecuteCommand(queryResetCacheVersion,
                               new SqlParameter("ObjectId", objectId),
                               new SqlParameter("Type", (int)type));

                            inserted = true;
                        }
                        else
                        {
                            HandleFailedInsert(ex, ref i);
                        }
                    }
                }
            }
        }

        protected void UpdateCacheVersionPersonalInDatabase(string userId, CacheVersionTypes type)
        {
            int i = 0;
            bool inserted = false;
            decimal objectId = 0;

            using (IDbConnection connection = sqlConnectionManager.GetNewOpenedSqlConnection(Constants.ALL, false))
            {
                ISqlWorker sqlWorker = sqlWorkerFactory.GetSqlWorker(connection);

                while (!inserted && i < this.configSettings.SqlMaxNumberOfInsertAttempts)
                {
                    try
                    {
                        objectId = RandomIdGenerator.Instance.GetRandomLong();

                        sqlWorker.ExecuteCommand(queryUpdateCacheVersionPersonal,
                            new SqlParameter("@ObjectId", objectId),
                            new SqlParameter("@UserId", userId),
                            new SqlParameter("@Type", (int)type));

                        inserted = true;

                    }
                    catch (SqlException ex)
                    {
                        if (ex.Number == (int)SqlErrors.ArithmeticOverflowConvertingToDataType)
                        {
                            sqlWorker.ExecuteCommand(queryResetCacheVersionPersonal,
                                new SqlParameter("@ObjectId", objectId),
                                new SqlParameter("@UserId", userId),
                                new SqlParameter("@Type", (int)type));

                            inserted = true;
                        }
                        else
                        {
                            HandleFailedInsert(ex, ref i);
                        }
                    }
                }
            }
        }

        protected int GetGlobalCacheVersion(CacheVersionTypes cacheVersionType)
        {
            int result = lockManagerGlobal.LockReadMode(() =>
            {
                if (cacheGetVersionFunctions.TryGetValue(cacheVersionType, out Func<int> getVersionFunction))
                {
                    return getVersionFunction.Invoke();
                }
                return 0;
            });

            return result;
        }

        protected void FillVersionInfo(VersionInfo result, CacheVersionDto cacheVersion)
        {
            int? changedVersion = null;

            if (cacheAssignVersionActions.TryGetValue((CacheVersionTypes)cacheVersion.CacheType, out Action<VersionInfo, int> action))
            {
                action.Invoke(result, (int)cacheVersion.Version);
            }
        }

        protected void AssignNewVersions(VersionInfo versionInfo)
        {
            foreach (var item in cacheGetVersionFunctions)
            {
                if (cacheAssignVersionActions.TryGetValue(item.Key, out Action<VersionInfo, int> assignAction))
                {
                    Func<int>  getFunction = item.Value;
                    assignAction.Invoke(versionInfo, getFunction.Invoke());
                }
            }
        }

        private int UpdateVersionIfChanged(int originalVerison, int newVersion, out int? changedVersion)
        {
            changedVersion = null;
            if (originalVerison != newVersion)
            {
                changedVersion = newVersion;
            }

            return newVersion;
        }

        private void EnsureCorrectMetadataVersion(bool forceReload = false)
        {
            bool needsUpdating = false;

            lockManagerGlobal.LockReadMode(() =>
            {
                needsUpdating = this.MetadataVersionNeedsUpdating(forceReload);
            });

            if (needsUpdating)
            {
                lockManagerGlobal.LockWriteMode(() =>
                {
                    if (this.MetadataVersionNeedsUpdating(forceReload))
                    {
                        this.MetadataVersion = metadataVersionRepository.GetMetadataVersion();
                        this.LastMetadataLoadTime = DateTime.Now;
                    }
                });
            }
        }

        private void InitializeCacheAssignVersionActionsDictionary()
        {
            cacheAssignVersionActions.Add(CacheVersionTypes.RecentFunctions, (x, y) => { x.RecentFunctions = UpdateVersionIfChanged(x.RecentFunctions, y, out int? newVersion); OnCacheVersionChange(CacheVersionTypes.RecentFunctions, newVersion); });
            cacheAssignVersionActions.Add(CacheVersionTypes.ListSettings, (x, y) => { x.ListSettings = UpdateVersionIfChanged(x.ListSettings, y, out int? newVersion); OnCacheVersionChange(CacheVersionTypes.ListSettings, newVersion); });
            cacheAssignVersionActions.Add(CacheVersionTypes.FunctionSettings, (x, y) => { x.FunctionSettings = UpdateVersionIfChanged(x.FunctionSettings, y, out int? newVersion); OnCacheVersionChange(CacheVersionTypes.FunctionSettings, newVersion); });
            cacheAssignVersionActions.Add(CacheVersionTypes.ApplicationSettings, (x, y) => { x.ApplicationSettings = UpdateVersionIfChanged(x.ApplicationSettings, y, out int? newVersion); OnCacheVersionChange(CacheVersionTypes.ApplicationSettings, newVersion); });
            cacheAssignVersionActions.Add(CacheVersionTypes.ConditionalFormat, (x, y) => { x.ConditionalFormat = UpdateVersionIfChanged(x.ConditionalFormat, y, out int? newVersion); OnCacheVersionChange(CacheVersionTypes.ConditionalFormat, newVersion); });
            cacheAssignVersionActions.Add(CacheVersionTypes.FunctionIcons, (x, y) => { x.FunctionIcons = UpdateVersionIfChanged(x.FunctionIcons, y, out int? newVersion); OnCacheVersionChange(CacheVersionTypes.FunctionIcons, newVersion); });
            cacheAssignVersionActions.Add(CacheVersionTypes.ReportSettings, (x, y) => { x.ReportSettings = UpdateVersionIfChanged(x.ReportSettings, y, out int? newVersion); OnCacheVersionChange(CacheVersionTypes.ReportSettings, newVersion); });
            cacheAssignVersionActions.Add(CacheVersionTypes.SystemSettings, (x, y) => { x.SystemSettings = UpdateVersionIfChanged(x.SystemSettings, y, out int? newVersion); OnCacheVersionChange(CacheVersionTypes.SystemSettings, newVersion); });
            cacheAssignVersionActions.Add(CacheVersionTypes.UserAccess, (x, y) => { x.UserAccess = UpdateVersionIfChanged(x.UserAccess, y, out int? newVersion); OnCacheVersionChange(CacheVersionTypes.UserAccess, newVersion); });
            cacheAssignVersionActions.Add(CacheVersionTypes.TileLayout, (x, y) => { x.TileLayout = UpdateVersionIfChanged(x.TileLayout, y, out int? newVersion); OnCacheVersionChange(CacheVersionTypes.TileLayout, newVersion); });
            cacheAssignVersionActions.Add(CacheVersionTypes.LiveTiles, (x, y) => { x.LiveTiles = UpdateVersionIfChanged(x.LiveTiles, y, out int? newVersion); OnCacheVersionChange(CacheVersionTypes.LiveTiles, newVersion); });
            cacheAssignVersionActions.Add(CacheVersionTypes.HelpText, (x, y) => { x.HelpText = UpdateVersionIfChanged(x.HelpText, y, out int? newVersion); OnCacheVersionChange(CacheVersionTypes.HelpText, newVersion); });
            cacheAssignVersionActions.Add(CacheVersionTypes.Company, (x, y) => { x.Company = UpdateVersionIfChanged(x.Company, y, out int? newVersion); OnCacheVersionChange(CacheVersionTypes.Company, newVersion); });
            cacheAssignVersionActions.Add(CacheVersionTypes.Index, (x, y) => { x.Index = UpdateVersionIfChanged(x.Index, y, out int? newVersion); OnCacheVersionChange(CacheVersionTypes.Index, newVersion); });
            cacheAssignVersionActions.Add(CacheVersionTypes.Dictionary, (x, y) => { x.Dictionary = UpdateVersionIfChanged(x.Dictionary, y, out int? newVersion); OnCacheVersionChange(CacheVersionTypes.Dictionary, newVersion); });
            cacheAssignVersionActions.Add(CacheVersionTypes.XmlType, (x, y) => { x.XmlType = UpdateVersionIfChanged(x.XmlType, y, out int? newVersion); OnCacheVersionChange(CacheVersionTypes.XmlType, newVersion); });
            cacheAssignVersionActions.Add(CacheVersionTypes.Profiler, (x, y) => { x.Profiler = UpdateVersionIfChanged(x.Profiler, y, out int? newVersion); OnCacheVersionChange(CacheVersionTypes.Profiler, newVersion); });
            cacheAssignVersionActions.Add(CacheVersionTypes.Workflow, (x, y) => { x.Workflow = UpdateVersionIfChanged(x.Workflow, y, out int? newVersion); OnCacheVersionChange(CacheVersionTypes.Workflow, newVersion); });
            cacheAssignVersionActions.Add(CacheVersionTypes.RestfullSettings, (x, y) => { x.RestfullSettings = UpdateVersionIfChanged(x.RestfullSettings, y, out int? newVersion); OnCacheVersionChange(CacheVersionTypes.RestfullSettings, newVersion); });
            cacheAssignVersionActions.Add(CacheVersionTypes.CustomFunctions, (x, y) => { x.CustomFunctions = UpdateVersionIfChanged(x.CustomFunctions, y, out int? newVersion); OnCacheVersionChange(CacheVersionTypes.CustomFunctions, newVersion); });
            cacheAssignVersionActions.Add(CacheVersionTypes.NonCacheableClasses, (x, y) => { x.NonCacheableClasses = UpdateVersionIfChanged(x.NonCacheableClasses, y, out int? newVersion); OnCacheVersionChange(CacheVersionTypes.NonCacheableClasses, newVersion); });
            cacheAssignVersionActions.Add(CacheVersionTypes.Diagram, (x, y) => { x.DiagramMapping = UpdateVersionIfChanged(x.DiagramMapping, y, out int? newVersion); OnCacheVersionChange(CacheVersionTypes.Diagram, newVersion); });
            cacheAssignVersionActions.Add(CacheVersionTypes.DiagramType, (x, y) => { x.DiagramDefinition = UpdateVersionIfChanged(x.DiagramDefinition, y, out int? newVersion); OnCacheVersionChange(CacheVersionTypes.DiagramType, newVersion); });
            cacheAssignVersionActions.Add(CacheVersionTypes.MSTeams, (x, y) => { x.MSTeams = UpdateVersionIfChanged(x.MSTeams, y, out int? newVersion); OnCacheVersionChange(CacheVersionTypes.MSTeams, newVersion); });
            cacheAssignVersionActions.Add(CacheVersionTypes.CustomMessages, (x, y) => { x.CustomMessages = UpdateVersionIfChanged(x.CustomMessages, y, out int? newVersion); OnCacheVersionChange(CacheVersionTypes.CustomMessages, newVersion); });
            cacheAssignVersionActions.Add(CacheVersionTypes.ExternalQueues, (x, y) => { x.ExternalQueues = UpdateVersionIfChanged(x.ExternalQueues, y, out int? newVersion); OnCacheVersionChange(CacheVersionTypes.ExternalQueues, newVersion); });
            cacheAssignVersionActions.Add(CacheVersionTypes.TaskSchedulers, (x, y) => { x.TaskSchedulers = UpdateVersionIfChanged(x.TaskSchedulers, y, out int? newVersion); OnCacheVersionChange(CacheVersionTypes.TaskSchedulers, newVersion); });
            cacheAssignVersionActions.Add(CacheVersionTypes.Events, (x, y) => { x.Events = UpdateVersionIfChanged(x.Events, y, out int? newVersion); OnCacheVersionChange(CacheVersionTypes.Events, newVersion); });
            cacheAssignVersionActions.Add(CacheVersionTypes.CICSetup, (x, y) => { x.CICSetup = UpdateVersionIfChanged(x.CICSetup, y, out int? newVersion); OnCacheVersionChange(CacheVersionTypes.CICSetup, newVersion); });
        }

        private void InitializeCacheGetVersionFunctionsDictionary()
        {
            cacheGetVersionFunctions.Add(CacheVersionTypes.Metadata, () => { return 0; });
            cacheGetVersionFunctions.Add(CacheVersionTypes.RecentFunctions, () => { return this.Versions.RecentFunctions; });
            cacheGetVersionFunctions.Add(CacheVersionTypes.ListSettings, () => { return this.Versions.ListSettings; });
            cacheGetVersionFunctions.Add(CacheVersionTypes.FunctionSettings, () => { return this.Versions.FunctionSettings; });
            cacheGetVersionFunctions.Add(CacheVersionTypes.ApplicationSettings, () => { return this.Versions.ApplicationSettings; });
            cacheGetVersionFunctions.Add(CacheVersionTypes.ConditionalFormat, () => { return this.Versions.ConditionalFormat; });
            cacheGetVersionFunctions.Add(CacheVersionTypes.FunctionIcons, () => { return this.Versions.FunctionIcons; });
            cacheGetVersionFunctions.Add(CacheVersionTypes.ReportSettings, () => { return this.Versions.ReportSettings; });
            cacheGetVersionFunctions.Add(CacheVersionTypes.SystemSettings, () => { return this.Versions.SystemSettings; });
            cacheGetVersionFunctions.Add(CacheVersionTypes.UserAccess, () => { return this.Versions.UserAccess; });
            cacheGetVersionFunctions.Add(CacheVersionTypes.TileLayout, () => { return this.Versions.TileLayout; });
            cacheGetVersionFunctions.Add(CacheVersionTypes.LiveTiles, () => { return this.Versions.LiveTiles; });
            cacheGetVersionFunctions.Add(CacheVersionTypes.HelpText, () => { return this.Versions.HelpText; });
            cacheGetVersionFunctions.Add(CacheVersionTypes.Company, () => { return this.Versions.Company; });
            cacheGetVersionFunctions.Add(CacheVersionTypes.Index, () => { return this.Versions.Index; });
            cacheGetVersionFunctions.Add(CacheVersionTypes.Dictionary, () => { return this.Versions.Dictionary; });
            cacheGetVersionFunctions.Add(CacheVersionTypes.XmlType, () => { return this.Versions.XmlType; });
            cacheGetVersionFunctions.Add(CacheVersionTypes.Profiler, () => { return this.Versions.Profiler; });
            cacheGetVersionFunctions.Add(CacheVersionTypes.Workflow, () => { return this.Versions.Workflow; });
            cacheGetVersionFunctions.Add(CacheVersionTypes.RestfullSettings, () => { return this.Versions.RestfullSettings; });
            cacheGetVersionFunctions.Add(CacheVersionTypes.CustomFunctions, () => { return this.Versions.CustomFunctions; });
            cacheGetVersionFunctions.Add(CacheVersionTypes.NonCacheableClasses, () => { return this.Versions.NonCacheableClasses; });
            cacheGetVersionFunctions.Add(CacheVersionTypes.Diagram, () => { return this.Versions.DiagramMapping; });
            cacheGetVersionFunctions.Add(CacheVersionTypes.DiagramType, () => { return this.Versions.DiagramDefinition; });
            cacheGetVersionFunctions.Add(CacheVersionTypes.MSTeams, () => { return this.Versions.MSTeams; });
            cacheGetVersionFunctions.Add(CacheVersionTypes.CustomMessages, () => { return this.Versions.CustomMessages; });
            cacheGetVersionFunctions.Add(CacheVersionTypes.ExternalQueues, () => { return this.Versions.ExternalQueues; });
            cacheGetVersionFunctions.Add(CacheVersionTypes.TaskSchedulers, () => { return this.Versions.TaskSchedulers; });
            cacheGetVersionFunctions.Add(CacheVersionTypes.Events, () => { return this.Versions.Events; });
            cacheGetVersionFunctions.Add(CacheVersionTypes.CICSetup, () => { return this.Versions.CICSetup; });
        }

        private void InitializePersonalCacheVersionSearchDictionary()
        {
            PersonalCacheversionsSearch.Add(CacheVersionTypes.Invalid, (VersionInfo info) => 0);
            PersonalCacheversionsSearch.Add(CacheVersionTypes.UserAccess, (VersionInfo info) => info.UserAccess);
            PersonalCacheversionsSearch.Add(CacheVersionTypes.Workflow, (VersionInfo info) => info.Workflow);
            PersonalCacheversionsSearch.Add(CacheVersionTypes.Profiler, (VersionInfo info) => info.Profiler);
            PersonalCacheversionsSearch.Add(CacheVersionTypes.SystemSettings, (VersionInfo info) => info.SystemSettings);
            PersonalCacheversionsSearch.Add(CacheVersionTypes.Metadata, (VersionInfo info) => 0);
            PersonalCacheversionsSearch.Add(CacheVersionTypes.HelpText, (VersionInfo info) => info.HelpText);
            PersonalCacheversionsSearch.Add(CacheVersionTypes.XmlType, (VersionInfo info) => info.XmlType);
            PersonalCacheversionsSearch.Add(CacheVersionTypes.ListSettings, (VersionInfo info) => info.ListSettings);
            PersonalCacheversionsSearch.Add(CacheVersionTypes.Company, (VersionInfo info) => info.Company);
            PersonalCacheversionsSearch.Add(CacheVersionTypes.ConditionalFormat, (VersionInfo info) => info.ConditionalFormat);
            PersonalCacheversionsSearch.Add(CacheVersionTypes.FunctionIcons, (VersionInfo info) => info.FunctionIcons);
            PersonalCacheversionsSearch.Add(CacheVersionTypes.TileLayout, (VersionInfo info) => info.TileLayout);
            PersonalCacheversionsSearch.Add(CacheVersionTypes.LiveTiles, (VersionInfo info) => info.LiveTiles);                
            PersonalCacheversionsSearch.Add(CacheVersionTypes.Index, (VersionInfo info) => info.Index);
            PersonalCacheversionsSearch.Add(CacheVersionTypes.Dictionary, (VersionInfo info) => info.Dictionary);
            PersonalCacheversionsSearch.Add(CacheVersionTypes.FunctionSettings, (VersionInfo info) => info.FunctionSettings);
            PersonalCacheversionsSearch.Add(CacheVersionTypes.RecentFunctions, (VersionInfo info) => info.RecentFunctions);
            PersonalCacheversionsSearch.Add(CacheVersionTypes.ApplicationSettings, (VersionInfo info) => info.ApplicationSettings);
            PersonalCacheversionsSearch.Add(CacheVersionTypes.ReportSettings, (VersionInfo info) => info.ReportSettings);
            PersonalCacheversionsSearch.Add(CacheVersionTypes.RestfullSettings, (VersionInfo info) => info.RestfullSettings);
            PersonalCacheversionsSearch.Add(CacheVersionTypes.CustomFunctions, (VersionInfo info) => info.CustomFunctions);
            PersonalCacheversionsSearch.Add(CacheVersionTypes.NonCacheableClasses, (VersionInfo info) => info.NonCacheableClasses);
            PersonalCacheversionsSearch.Add(CacheVersionTypes.Diagram, (VersionInfo info) => info.DiagramMapping);
            PersonalCacheversionsSearch.Add(CacheVersionTypes.DiagramType, (VersionInfo info) => info.DiagramDefinition);
        }

        private bool MetadataVersionNeedsUpdating(bool forceReload)
        {
            return (forceReload || (DateTime.Now - this.LastMetadataLoadTime).TotalSeconds > reloadMetadataInterval);
        }

        protected readonly string queryGetCacheVersions =
            @"
            SELECT
                CONVERT(INT, [CacheType]) AS CacheType,
                [CacheVersion] AS Version
            FROM
                [Table_CacheVersion]
            ";

        protected readonly string queryGetCacheVersionsPersonal =
            @"
            SELECT
                CONVERT(INT, [_pk_CacheType]) AS CacheType,
                [CacheVersion] AS Version
            FROM
                [Table_CacheVersionPersonal]
            WHERE
                [_pk_UserId] = @UserId AND
                [Company] = '*' AND
                [State] = 1
            ";

        protected const string queryIncrement = "[AP0000085377] + 1";

        protected const string querySet = "@version";

        protected readonly string queryUpdateCacheVersion =
            @"
            UPDATE
                [CP0000005164_ALL] SET  
                [AP0000085377] = {0},
                [MODIFY_TIME] = CONVERT(DATETIME, CONVERT(VARCHAR(20), GETDATE(), 120)),
                [VERSION] = [VERSION] + 1
            WHERE
                [AP0000085376] = @Type AND
                [Company] = '*' AND
                [State] = 1 
            IF @@ROWCOUNT = 0
                INSERT INTO [CP0000005164_ALL]
                    ([OBJECT_ID],
                     [COMPANY],
                     [STATE],
                     [ADD_UID],
                     [ADD_TIME],
                     [MODIFY_UID],
                     [MODIFY_TIME],
                     [VERSION],
                     [AP0000085376],
                     [AP0000085377])
                VALUES
                    (@ObjectId,
                    '*',
                    1,
                    'Server',
                    CONVERT(DATETIME, CONVERT(VARCHAR(20), GETDATE(), 120)),
                    'Server',
                    CONVERT(DATETIME, CONVERT(VARCHAR(20), GETDATE(), 120)),
                    1,
                    @Type,
                    1)";

        protected readonly string queryUpdateCacheVersionPersonal =
            @"
            UPDATE
                [CP0000005292_ALL]
            SET
                [AP0000087348] = [AP0000087348] + 1,
                [MODIFY_TIME] =  CONVERT(DATETIME, CONVERT(VARCHAR(20), GETDATE(), 120)),
                [VERSION] = [VERSION] + 1
            WHERE
                [AP0000087347] = @UserId AND
                [AP0000087346] = @Type AND
                [Company] = '*' AND
                [State] = 1
            
            IF @@ROWCOUNT = 0 
                INSERT INTO [CP0000005292_ALL]
                    (
                    [OBJECT_ID],
                    [COMPANY],
                    [STATE],
                    [ADD_UID],
                    [ADD_TIME],
                    [MODIFY_UID],
                    [MODIFY_TIME],
                    [VERSION],
                    [AP0000087347],
                    [AP0000087346],
                    [AP0000087348]
                    )
                VALUES
                    (
                    @ObjectId,
                    '*',
                    1,
                    'Server',
                    CONVERT(DATETIME, CONVERT(VARCHAR(20), GETDATE(), 120)),
                    'Server',
                    CONVERT(DATETIME, CONVERT(VARCHAR(20), GETDATE(), 120)),
                    1,
                    @UserId,
                    @Type,
                    1
                    )
            ";

        protected readonly string queryResetCacheVersion =
            @"
            UPDATE
                [CP0000005164_ALL] SET
                [AP0000085377] = 1,
                [MODIFY_TIME] = CONVERT(DATETIME, CONVERT(VARCHAR(20), GETDATE(), 120)),
                [VERSION] = 1
            WHERE
                [AP0000085376] = @Type AND
                [Company] = '*' AND
                [State] = 1
            ";

        protected readonly string queryResetCacheVersionPersonal =
            @"
            UPDATE
                [CP0000005292_ALL]
            SET
                [AP0000087348] = 1,
                [MODIFY_TIME] = CONVERT(DATETIME, CONVERT(VARCHAR(20), GETDATE(), 120)),
                [VERSION] = 1
            WHERE
                [AP0000087347] = @UserId AND
                [AP0000087346] = @Type AND
                [Company] = '*' AND
                [State] = 1
            ";
    }
}
