﻿using Codeless.Framework.DependencyInjection;
using Codeless.Framework.IO.Standard;
using Codeless.Framework.Logging.Standard;
using Codeless.Framework.LockManagement;
using Codeless.Server.Common.Cache.CacheBaseClasses;
using Codeless.Server.Common.Cache.CacheVersions;
using Codeless.Server.Common.Cache.CustomStyleCache;
using Codeless.Server.Common.ConnectionInformation;
using Codeless.Server.Interfaces.Common.Cache;
using System;

namespace Codeless.Server.Common.Cache.UserFunctionSettings
{
    public class CustomStyleCache : DictionaryJITLoadCacheBase<string, string>, ICustomStyleCache
    {
        private IFileSystem fileSystem;
        private IPackageInfo packageInfo;

        public string GetCustomStyles(string fileName)
        {
            return GetCacheItem(fileName);
        }

        [InjectionMethod]
        public void Initialize(IPackageInfo packageInfo,
                               ICacheVersionsCache cacheVersionsCache,
                               ILockManagerFactory lockManagerFactory,
                               ILoggerFactory loggerFactory,
                               IFileSystem fileSystem)
        {

            base.Initialize(packageInfo, cacheVersionsCache, lockManagerFactory, loggerFactory, CacheVersionTypes.ApplicationSettings);
            this.fileSystem = fileSystem ?? throw new ArgumentNullException(nameof(fileSystem));
            this.packageInfo = packageInfo ?? throw new ArgumentNullException(nameof(packageInfo));
        }

        protected override string LoadDataItem(string key)
        {
            string customStylesData = null;
            string fileName = PathExt.Combine(packageInfo.GetDefaultPackage().ServerFiles.SettingsFolder, key);
            if (fileSystem.FileExists(fileName))
            {
                customStylesData = fileSystem.ReadAllText(fileName);
            }

            return customStylesData;
        }


        #region Singleton

        private CustomStyleCache() { }

        public static ICustomStyleCache Instance
        {
            get
            {
                return Nested.instance;
            }
        }

        class Nested
        {
            protected Nested()
            { }
            static Nested()
            {
            }

            internal static readonly ICustomStyleCache instance = new CustomStyleCache();
        }

        #endregion
    }
}
