﻿using System;
using System.Runtime.Serialization;

namespace Codeless.Server.Common.Exceptions
{
    [Serializable]
    public class FileLockedException : Exception
    {
        public FileLockedException(string message, Exception innerException)
           : base(message, innerException)
        {
        }

        public FileLockedException(string message)
         : base(message)
        {
        }

        protected FileLockedException(SerializationInfo info, StreamingContext context)
          : base(info, context)
        {
        }

        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            base.GetObjectData(info, context);
        }
    }
}
