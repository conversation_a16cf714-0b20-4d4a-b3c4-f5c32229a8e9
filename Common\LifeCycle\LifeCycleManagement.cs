﻿using Codeless.Framework.LifeCycleManagement;

namespace Codeless.Server.Common.LifeCycle
{
    public class LifeCycleSettingsProvider : ILifeCycleSettingsProvider
    {
        private readonly IConfigSettings configSettings;

        public LifeCycleSettingsProvider(IConfigSettings configSettings)
        {
            this.configSettings = configSettings ?? throw new System.ArgumentNullException(nameof(configSettings));
        }

        public bool HealthProbesEnabled => false;

        public int HealthProbesPort => configSettings.LifeCycleManagementHealthProbesPort;

        public int FinalizeWorkTimeout => configSettings.LifeCycleManagementFinalizeWorkTimeoutSeconds;

        public int ShutdownTimeout => configSettings.LifeCycleManagementShutdownTimeoutSeconds;

        public string ComponentName => "Server";

    }

    public enum StartupStep
    {
        ApplicationManifestOK,
        RedisTestedOK
    }

    public enum ShutdownStep
    {
        EventStoreStopped
    }
}
