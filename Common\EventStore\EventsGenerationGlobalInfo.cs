﻿using System;
using System.Collections.Generic;

namespace Codeless.Server.Common.EventStore
{
    public class EventsGenerationGlobalInfo
    {
        public int PendingRequests { get; set; }
        public DateTime? LastRequestAdded { get; set; }
        public DateTime? LastRequestStarted { get; set; }
        public DateTime? LastRequestFinished { get; set; }
        public int MaxHistorySize { get; set; } = 200;
        public int Workers { get; set; }
        public int MaxWorkers { get; set; }
        public List<EventsGenerationInfo> Requests { get; set; }

        public EventsGenerationGlobalInfo Clone()
        {
            return new EventsGenerationGlobalInfo()
            {
                PendingRequests = this.PendingRequests,
                LastRequestAdded = this.LastRequestAdded,
                LastRequestStarted = this.LastRequestStarted,
                LastRequestFinished = this.LastRequestFinished,
                MaxHistorySize = this.MaxHistorySize,
                Workers = this.Workers,
                MaxWorkers = this.MaxWorkers
            };
        }
    }
}