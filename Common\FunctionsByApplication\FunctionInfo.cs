﻿using System.Text.Json.Serialization;

namespace Codeless.Server.Common.FunctionsByApplication
{
    public class FunctionInfo
    {
        public string ApplicationName { get; set; }
        public int FunctionId { get; set; }

        [JsonIgnore]
        public bool IsRuntime => string.IsNullOrEmpty(ApplicationName);

        public FunctionInfo()
        {         
        }

        public FunctionInfo(int functionId, string applicationName)
        {
            FunctionId = functionId;
            ApplicationName = applicationName;
        }
    }
}
