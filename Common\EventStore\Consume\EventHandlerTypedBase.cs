﻿using Codeless.Framework.EventProcessing.Event;
using Codeless.Framework.EventProcessing.EventHandling;

namespace Codeless.Server.Common.EventStore.Consume
{
	public abstract class EventHandlerTypedBase<T> : EventHandlingBase<T, ModelContainer> where T : EventBase
	{
		protected EventHandlerTypedBase(ModelContainer modelContainer)
	   : base(modelContainer)
		{
		}

		public override string CreateUrlToEvent(long? eventNumber, string streamName) => null;
	}
}
