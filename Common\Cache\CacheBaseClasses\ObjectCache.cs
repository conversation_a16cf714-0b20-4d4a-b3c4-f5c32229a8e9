﻿using Codeless.Framework.Logging.Standard;
using Codeless.Framework.LockManagement;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;

namespace Codeless.Server.Common.Cache.CacheBaseClasses
{
    /// <summary>
    /// Generic object cache with max size. Based on objects beeing removed from caching while beeing used.
    /// Use ObjectCacheStringKey if the key is a string to support formatting.
    /// </summary>
    /// <typeparam name="KeyType"></typeparam>
    /// <typeparam name="ContentType"></typeparam>
    public class ObjectCache<KeyType, ContentType> where ContentType : class
    {
        // Dependencies
        private readonly ILogger logger;
        private readonly ILockManager lockManager;

        private readonly ConcurrentDictionary<KeyType, ObjectCacheItem<ContentType, KeyType>> cache = new ConcurrentDictionary<KeyType, ObjectCacheItem<ContentType, KeyType>>();
        private readonly HashSet<KeyType> knownKeys = new HashSet<KeyType>();

        private readonly int? maxSize = null;
        private readonly TimeSpan? ttl = null;

        private int currentSize;

        // Double linked list is used maintain cache items in order of last recently used.
        private ObjectCacheItem<ContentType, KeyType> cacheHead;
        private ObjectCacheItem<ContentType, KeyType> cacheTail;

        public Dictionary<KeyType, ContentType> GetAll()
        {
            return lockManager.LockReadMode(() =>
            {
                Dictionary<KeyType, ContentType> values = new Dictionary<KeyType, ContentType>();

                if (cacheHead != null)
                {
                    values.Add(cacheHead.Key, cacheHead.Content);
                    ObjectCacheItem<ContentType, KeyType> item = cacheHead.NextCacheItem;
                    while (item != null)
                    {
                        values.Add(item.Key, item.Content);
                        item = item.NextCacheItem;
                    }
                }

                return values;
            });
        }

        public void Clear()
        {
            lockManager.LockWriteMode(() =>
            {
                cache.Clear();
                knownKeys.Clear();
                currentSize = 0;
                cacheHead = null;
                cacheTail = null;
            });
        }

        public ObjectCache(int maxSize, ILoggerFactory loggerfactory, ILockManagerFactory lockManagerFactory) : this(maxSize, null, loggerfactory, lockManagerFactory)
        {
            
        }

        public ObjectCache(TimeSpan ttl, ILoggerFactory loggerfactory, ILockManagerFactory lockManagerFactory) : this(null, ttl, loggerfactory, lockManagerFactory)
        {
            
        }

        private ObjectCache(int? maxSize, TimeSpan? ttl, ILoggerFactory loggerfactory, ILockManagerFactory lockManagerFactory)
        {
            loggerfactory = loggerfactory ?? throw new ArgumentNullException(nameof(loggerfactory));
            lockManagerFactory = lockManagerFactory ?? throw new ArgumentNullException(nameof(lockManagerFactory));

            string type1 = typeof(KeyType).FullName;
            string type2 = typeof(ContentType).FullName;
            string typeName = $"ObjectCache<{type1}, {type2}>";

            this.logger = loggerfactory.GetLogger(typeof(ObjectCache<KeyType,ContentType>));
            this.lockManager = lockManagerFactory.GetLockManager(typeName);
            this.maxSize = maxSize;
            this.ttl = ttl;
        }

        public virtual ContentType TryRemove(KeyType key, out int? size)
        {
            (ContentType result, int? foundSize) = lockManager.LockWriteMode(() =>
            {
                ContentType content = TryRemove(key, true, out int? newSize);
                return (content, newSize);
            });

            size = foundSize;
            return result;
        }

        private ContentType TryRemove(KeyType key, bool forUse, out int? size)
        {
            ObjectCacheItem<ContentType, KeyType> cacheItem;
            ContentType content = null;
            cache.TryRemove(key, out cacheItem);
            size = null;

            if (cacheItem != null)
            {
                content = cacheItem.Content;
                size = cacheItem.Size;

                currentSize -= cacheItem.Size;
                RemoveFromLinkedList(cacheItem);

                if (forUse)
                {
                    if (logger.IsDebugEnabled)
                    {
                        logger.WriteDebugFormat("Removed for use {0}, Count={1}, Size={2}", key, cache.Count, currentSize);
                    }
                }
                else
                {
                    knownKeys.Remove(key);
                    if (logger.IsDebugEnabled)
                    {
                        logger.WriteDebugFormat("Removed for clean up {0}, Count={1}, Size={2}", key, cache.Count, currentSize);
                    }
                }
            }

            return content;
        }

        public virtual bool TryGet(KeyType key, out ContentType content)
        {
            content = null;
            cache.TryGetValue(key, out ObjectCacheItem<ContentType, KeyType> cacheItem);

            if (cacheItem != null)
            {
                content = cacheItem.Content;
                MoveToHeadLinkedList(cacheItem);
            }

            return content != null;
        }

        public virtual bool CheckEvictionAndTryGet(KeyType key, out ContentType content)
        {
            CheckEviction();
            return TryGet(key, out content);
        }

        // TO-DO: Add a TryUpdate here instead of writting it everywhere else

        public virtual bool TryAdd(KeyType key, ContentType content, int size)
        {
            _ = content ?? throw new ArgumentNullException(nameof(content), "Content is not allowed to be null.");

            ObjectCacheItem<ContentType, KeyType> cacheItem = new ObjectCacheItem<ContentType, KeyType>(content, key, size);

            bool added = cache.TryAdd(key, cacheItem);

            if (added)
            {
                lockManager.LockWriteMode(() =>
                {
                    if (!knownKeys.Contains(key))
                    {
                        knownKeys.Add(key);
                    }

                    CheckEviction();
                    currentSize += cacheItem.Size;
                    AddToLinkedList(cacheItem);

                    if (logger.IsDebugEnabled)
                    {
                        logger.WriteDebugFormat("Added {0}, Count={1}, Size={2}", key, cache.Count, currentSize);
                    }
                });
            }

            return added;
        }

        public virtual bool IsKnownKey(KeyType key)
        {
            return knownKeys.Contains(key);
        }

        private void AddToLinkedList(ObjectCacheItem<ContentType, KeyType> cacheItem)
        {
            if (cacheHead != null)
            {
                cacheHead.PreviousCacheItem = cacheItem;
            }
            cacheItem.PreviousCacheItem = null;
            cacheItem.NextCacheItem = cacheHead;

            cacheHead = cacheItem;

            if (cacheTail == null)
            {
                cacheTail = cacheItem;
            }
        }

        private void RemoveFromLinkedList(ObjectCacheItem<ContentType, KeyType> cacheItem)
        {
            lockManager.LockWriteMode(() =>
            {
                if (cacheItem.PreviousCacheItem != null)
                {
                    cacheItem.PreviousCacheItem.NextCacheItem = cacheItem.NextCacheItem;
                }
                else
                {
                    if (cacheItem.NextCacheItem != null)
                    {
                        cacheItem.NextCacheItem.PreviousCacheItem = null;
                    }
                    cacheHead = cacheItem.NextCacheItem;
                }

                if (cacheItem.NextCacheItem != null)
                {
                    cacheItem.NextCacheItem.PreviousCacheItem = cacheItem.PreviousCacheItem;
                }
                else
                {
                    if (cacheItem.PreviousCacheItem != null)
                    {
                        cacheItem.PreviousCacheItem.NextCacheItem = null;
                    }
                    cacheTail = cacheItem.PreviousCacheItem;
                }
            });
        }

        public void MoveToHeadLinkedList(ObjectCacheItem<ContentType, KeyType> cacheItem)
        {
            lockManager.LockWriteMode(() =>
            {
                // Only change something if not yet the head
                if (cacheItem.PreviousCacheItem != null)
                {
                    if (cacheItem.NextCacheItem == null)
                    {
                        // Item was the tail, set new tail
                        cacheItem.PreviousCacheItem.NextCacheItem = null;
                        cacheTail = cacheItem.PreviousCacheItem;
                    }
                    else
                    {
                        // Link previous and next together
                        cacheItem.PreviousCacheItem.NextCacheItem = cacheItem.NextCacheItem;
                        cacheItem.NextCacheItem.PreviousCacheItem = cacheItem.PreviousCacheItem;
                    }

                    // Set item as new head
                    cacheHead.PreviousCacheItem = cacheItem;
                    cacheItem.PreviousCacheItem = null;
                    cacheItem.NextCacheItem = cacheHead;
                    cacheHead = cacheItem;
                    cacheItem.LastAcccessed = DateTime.Now;
                }
            });
        }

        public ContentType GetFirstItem()
        {
            return lockManager.LockReadMode(() =>
            {
                return this.cacheHead?.Content;
            });
        }

        public ContentType GetLastItem()
        {
            return lockManager.LockReadMode(() =>
            {
                return this.cacheTail?.Content;
            });
        }

        /// <summary>
        /// Check if cache exceeds maximum amount of objects, if true the oldest items that is not protected are removed untill cache size is ok.
        /// </summary>
        private void CheckEviction()
        {
            if (maxSize.HasValue)
            {
                CheckMaxCacheSize();
            }
            else
            {
                CheckTTL();
            }
        }

        private void CheckMaxCacheSize()
        {
            while (cacheTail != null && currentSize > maxSize)
            {
                ContentType contentType = TryRemove(cacheTail.Key, false, out _);

                if (contentType == null)
                {
                    break;
                }

                ICleanup contentCleanup = contentType as ICleanup;

                if (contentCleanup != null)
                {
                    contentCleanup.Clean();
                }
            }
        }

        private void CheckTTL()
        {
            DateTime evictionTime = DateTime.Now.Subtract(this.ttl.Value);
            bool removed = true;

            while (cacheTail != null && removed)
            {
                if (cacheTail.LastAcccessed < evictionTime)
                {
                    ContentType contentType = TryRemove(cacheTail.Key, false, out _);
                    ICleanup contentCleanup = contentType as ICleanup;

                    removed = contentType != null;

                    if (contentType != null && contentCleanup != null)
                    {
                        contentCleanup.Clean();
                    }
                }
                else
                {
                    removed = false;
                }
            }
        }

        public int Count
        {
            get
            {
                return lockManager.LockReadMode(() =>
                {
                    return cache.Count;
                });
            }
        }

        public int Size
        {
            get
            {
                return lockManager.LockReadMode(() =>
                {
                    return currentSize;
                });
            }
        }
    }
}