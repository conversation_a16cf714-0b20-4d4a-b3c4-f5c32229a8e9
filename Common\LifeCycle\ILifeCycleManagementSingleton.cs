﻿using Codeless.Framework.LifeCycleManagement;

namespace Codeless.Server.Common.LifeCycle
{
    public interface ILifeCycleManagementSingleton
    {
        ILifeCycleManager<StartupStep, ShutdownStep> GetLifeCycleManagement { get; }

        bool AcceptingWork { get; }
        bool InStartup { get; }
        bool IsDead { get; }

        void StartupCompleted(StartupStep startupStep);
        void ShutdownCompleted(ShutdownStep shutdownStep);
        void WorkStart();
        void WorkDone();
        void RequestShutdown();

    }
}