﻿using Codeless.Server.Common.RuntimeClasses;
using Codeless.Server.MetaClasses;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Codeless.Server.Common.Helper
{
    public class RelayClassHelper
    {
        private readonly List<int> runtimeClassIds;
        private readonly List<int> nonRuntimeClassIds;

        public RelayClassHelper(IMetaData metaData, IRuntimeClassesProvider runtimeClassesProvider)
        {
            _ = metaData ?? throw new ArgumentNullException(nameof(metaData));
            _ = runtimeClassesProvider ?? throw new ArgumentNullException(nameof(runtimeClassesProvider));

            var allClassIds = metaData.Package.AllClassIds;
            runtimeClassIds = allClassIds.Where(runtimeClassesProvider.IsRuntimeClass).ToList();
            nonRuntimeClassIds = allClassIds.Except(runtimeClassIds).ToList();
        }

        public List<int> RuntimeClassIds
        
        { 
            get 
            { 
                return runtimeClassIds; 
            } 
        }

        public List<int> NonRuntimeClassIds
        {
            get
            {
                return nonRuntimeClassIds;
            }
        }
    }
}