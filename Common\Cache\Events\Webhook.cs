﻿namespace Codeless.Server.Common.Cache.Events
{
    public class Webhook
    {
        public int WebhookId { get; set; }
        public long WebhookObjectId { get; set; }
        public string EventTypeName { get; set; }
        public int EventTypeId { get; set; }
        public long EventTypeObjectId { get; set; }
        public string URL { get; set; }
        public Method Method { get; set; }
        public AuthenticationType AuthenticationType { get; set; }
        public string AuthenticationURL { get; set; }
        public string BasicPassword { get; set; }
        public string BasicUserId { get; set; }
        public BodyType BodyType { get; set; }
        public string HttpHeaders { get; set; }
        public bool InternalCall { get; set; }
        public bool LogCallDetails { get; set; }
        public string Office365oAuthClientId { get; set; }
        public Office365oAuthMode Office365oAuthMode { get; set; }
        public string Office365oAuthSecret { get; set; }
        public string Office365oAuthTenantId { get; set; }
        public string PackageGUID { get; set; }
        public string UserHashedPassword { get; set; }
        public string UserId { get; set; }
        public string XmlTypeName { get; set; }
        public int XmlTypeMajorVersion { get; set; }    
    }

    public enum AuthenticationType
    {
        CurrentUserCredentials = 0,
        NoAuthentication = 1,
        CustomCredentials = 2,
        ServiceAccountCredentials = 3,
        ThirdPartyCustom = 4,
        ThirdPartyServiceAccount = 5,
        WindowsAuthentication = 6
    }

    public enum BodyType
    {
        Xml = 0,
        Json = 1
    }

    public enum Method
    {
        PUT = 0,
        POST = 1
    }   

    public enum Office365oAuthMode
    {
        None = 0,
        ConfiguredSettings = 1,
        CustomSettings = 2
    }
}
