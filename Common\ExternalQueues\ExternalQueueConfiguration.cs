﻿using Codeless.Framework.ExternalQueue;
using System;
using System.Diagnostics;
using System.Linq;

namespace Codeless.Server.Common
{
    public static class ExternalQueueConfiguration
    {
        public static IExternalQueueManager ExternalQueueManager { get; private set; }

        public static bool CICMessageReaderEnabled
            => !Debugger.IsAttached && ExternalQueueManager.GetExternalQueues(ExternalQueueScope.CICImport).Count != 0;

        public static void Initialize(IExternalQueueManager externalQueueManager)
        {
            ExternalQueueManager = externalQueueManager ?? throw new ArgumentNullException(nameof(externalQueueManager));
        }
    }
}