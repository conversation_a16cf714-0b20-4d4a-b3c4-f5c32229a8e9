﻿using Codeless.Framework.Logging.Standard;
using Codeless.Framework.LockManagement;
using Codeless.Server.Common.Cache.CacheVersions;
using Codeless.Server.Common.ConnectionInformation;
using Codeless.Server.Interfaces.Common.Cache;

namespace Codeless.Server.Common.Cache.CacheBaseClasses
{
    public abstract class ObjectCacheJITLoadBase<TValue, TKey> : CacheBase where TValue : class, ICleanup
    {
        protected ObjectCache<TKey, TValue> items;

        protected abstract TValue LoadDataItem(TKey key);

        protected override void PreLoadData()
        {
            // Do not preload a dictionary, so do nothing here
        }

        protected TValue GetCacheItem(TKey key)
        {
            EnsureCorrectDataLoaded();

            TValue item = default(TValue);
            bool inCache = false;
            this.lockManager.LockReadMode(() =>
            {
                inCache = this.items.TryGet(key, out item);
            });

            if (!inCache)
            {
                this.lockManager.LockUpgradeMode(() =>
                {
                    if (!this.items.TryGet(key, out item))
                    {
                        item = this.LoadDataItem(key);

                        this.lockManager.LockWriteMode(() =>
                        {
                            items.TryAdd(key, item, 1);
                        });
                    }
                });
            }

            return item;
        }

        protected bool TryGetCacheItem(TKey key, out TValue item)
        {
            item = GetCacheItem(key);
            return true;
        }

        protected bool Contains(TKey key)
        {
            EnsureCorrectDataLoaded();

            return this.lockManager.LockReadMode(() =>
            {
                return this.items.TryGet(key, out TValue value);
            });
        }

        public override int Count()
        {
            EnsureCorrectDataLoaded();

            return base.lockManager.LockReadMode(() =>
            {
                return this.items.Count;
            });
        }

        public override void Flush()
        {
            this.lockManager.LockWriteMode(() =>
            {
                this.items.Clear();
                this.loadedVersion = null;
            });
        }

        public void Initialize(
            int maxSize, 
            IPackageInfo packageInfo, 
            ICacheVersionsCache cacheVersionsCache, 
            ILockManagerFactory lockManagerFactory, 
            ILoggerFactory loggerFactory,
            CacheVersionTypes cacheVersionType)
        {
            items = new ObjectCache<TKey, TValue>(maxSize, loggerFactory, lockManagerFactory);
            Initialize(packageInfo, cacheVersionsCache, lockManagerFactory, loggerFactory, cacheVersionType);
        }
    }
}