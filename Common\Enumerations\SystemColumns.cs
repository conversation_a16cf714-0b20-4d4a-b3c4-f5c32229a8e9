﻿using System.Collections.Generic;

namespace Codeless.Server.Common.Enumerations
{
    public enum SystemColumnId
    {
        State = -1,
        Company = -2,
        AddedBy = -3,
        AddTime = -4,
        ModifiedBy = -5,
        ModifyTime = -6,
        FriendlyName = -7,
        Version = -8,
        ForeignKey = -9,
        ObjectId = -10
    }

    public static class SystemColumnNames
    {
        public const string State = "STATE";
        public const string Company = "COMPANY";
        public const string AddedBy = "ADD_UID";
        public const string AddTime = "ADD_TIME";
        public const string ModifiedBy = "MODIFY_UID";
        public const string ModifyTime = "MODIFY_TIME";
        public const string FriendlyName = "FRIENDLY_NAME";
        public const string Version = "VERSION";
        public const string ObjectId = "OBJECT_ID";
        public const string ObjectIdTemp = "OBJECT_ID_TEMP";

        public const string ColumnName = "ColumnName";
        public const string ConversionValue = "ConversionValue";

        private static HashSet<string> names = new HashSet<string>
        {
            AddedBy,
            AddTime,
            ModifiedBy,
            ModifyTime,
            Company,
            FriendlyName,
            ObjectId,
            State,
            Version
        };

        public static bool IsSystemColumn(string attributeName)
        {
            return names.Contains(attributeName.ToUpper());
        }
    }

    public static class MongoSystemColumnNames
    {
        public const string Id = "_id";
    }
}