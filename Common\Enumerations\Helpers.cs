﻿using System;

namespace Codeless.Server.Common.Enumerations
{
    public static class Helpers
    {
        public static TEnum ValueToEnum<TEnum>(object value) where TEnum : struct
        {
            if (null == value)
            {
                throw new Exception(string.Format("Invalid value for type {0}. Value is null.", typeof(TEnum).Name));
            }
            else if (Enum.IsDefined(typeof(TEnum), value))
            {
                return (TEnum)Enum.Parse(typeof(TEnum), value.ToString());
            }
            else
            {
                throw new Exception(string.Format("Invalid value for type {0}. Value is {1}.", typeof(TEnum).Name, value));
            }
        }

        public static TEnum? ValueToEnumNullable<TEnum>(object value) where TEnum : struct
        {
            if (null == value)
            {
                return null;
            }
            else
            {
                return ValueToEnum<TEnum>(value);
            }
        }
    }
}