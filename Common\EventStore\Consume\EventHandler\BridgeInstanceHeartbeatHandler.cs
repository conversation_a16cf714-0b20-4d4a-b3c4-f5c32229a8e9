﻿using Codeless.Framework.EventProcessing.Event;
using Codeless.Framework.EventProcessing.Event.Contracts.Bridge.Events;
using Codeless.Framework.EventProcessing.EventHandling;
using Codeless.Server.Common.Cache.BridgeHeartbeats;
using Codeless.Server.Common.EventStore.DTO;
using System;

namespace Codeless.Server.Common.EventStore.Consume.EventHandler
{
    public class BridgeInstanceHeartbeatHandler : HandlerBase<BridgeInstanceHeartbeatExecuted>
    {
        public BridgeInstanceHeartbeatHandler(ModelContainer modelContainer) : base(modelContainer) { }

        protected override void ProcessEvent(BridgeInstanceHeartbeatExecuted eventObject, DateTime eventTime, RelatedEvent relatedEvent)
        {
            foreach (var connectionName in eventObject.TargetStorageConnectionNames)
            {
                BridgeHeartbeatsCache.Instance.UpdateBridgeHeartbeat(connectionName,
                    new BridgeInstanceHeartbeat()
                    {
                        BrdigeHeartbeatTime = eventTime,
                        SourceServerHeartbeatInterval = eventObject.SourceServerHeartbeatInterval,
                        SourceServerHeartbeatTime = eventObject.SourceServerHeartbeat
                    });
            }
        }
    }
}
