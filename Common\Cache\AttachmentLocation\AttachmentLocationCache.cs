﻿using Codeless.Framework.DependencyInjection;
using Codeless.Framework.Logging.Standard;
using Codeless.Framework.Sql.Standard;
using Codeless.Framework.LockManagement;
using Codeless.Server.Common.Cache.CacheBaseClasses;
using Codeless.Server.Common.Cache.CacheVersions;
using Codeless.Server.Common.ConnectionInformation;
using Codeless.Server.Interfaces.Common.Cache;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text.RegularExpressions;

namespace Codeless.Server.Common.Cache.AttachmentLocation
{
    public class AttachmentLocationCache : DictionaryCacheBase<int, CustomAttachmentLocation>, IAttachmentLocationCache
    {
        private ISqlConnectionManager sqlConnectionManager;
        private ISqlWorkerFactory sqlWorkerFactory;
        private IConfigSettings configSettings;

        public CustomAttachmentLocation GetCustomLocation(int classId) => GetCacheItem(classId);

        protected override void PreLoadData()
        {
            items.Clear();
            using (IDbConnection connection = sqlConnectionManager.GetNewOpenedSqlConnection(Constants.ALL, false))
            {
                ISqlWorker worker = sqlWorkerFactory.GetSqlWorker(connection);
                List<CustomAttachmentLocation> customAttachmentLocations = worker.ExecuteQuery<CustomAttachmentLocation>(queryGetCustomAttachmentLocations).ToList();

                foreach (CustomAttachmentLocation item in customAttachmentLocations)
                {
                    item.TokenAttributeIds = GetTokenAttributeIds(item);
                    items.Add(item.ClassId, item);
                }
            }
        }

        private List<int> GetTokenAttributeIds(CustomAttachmentLocation customLocation)
        {
            List<int> attributeIds = new List<int>();

            MatchCollection matches = Regex.Matches(customLocation.AttachmentLocation, @"\[\d+\]", RegexOptions.None, TimeSpan.FromMilliseconds(configSettings.RegExTimeoutMs));
            foreach (Match match in matches)
            {
                foreach (Capture capture in match.Captures)
                {
                    attributeIds.Add(Convert.ToInt32(capture.Value.Replace("[", string.Empty).Replace("]", string.Empty)));
                }
            }

            return attributeIds;
        }

        [InjectionMethod]
        public void Initialize(IPackageInfo packageInfo,
                               ICacheVersionsCache cacheVersionsCache,
                               ILockManagerFactory lockManagerFactory,
                               ILoggerFactory loggerFactory,
                               ISqlConnectionManager sqlConnectionManager,
                               ISqlWorkerFactory sqlWorkerFactory,
                               IConfigSettings configSettings)
        {
            this.sqlConnectionManager = sqlConnectionManager ?? throw new ArgumentNullException(nameof(sqlConnectionManager));
            this.sqlWorkerFactory = sqlWorkerFactory ?? throw new ArgumentNullException(nameof(sqlWorkerFactory));
            this.configSettings = configSettings ?? throw new ArgumentNullException(nameof(configSettings));

            base.Initialize(packageInfo, cacheVersionsCache, lockManagerFactory, loggerFactory, CacheVersionTypes.ApplicationSettings);
        }

        private static readonly string queryGetCustomAttachmentLocations =
            @"
             SELECT _pk_ClassId AS ClassId,
                    DisableAttachmentsServiceCache AS DisableAttachmentsServiceCache,
                    DeleteFilesFromAttachmentAttributes AS DeleteFilesFromAttachmentAttributes,
                    DeleteAllAttachmentsOnObjectDelete AS DeleteAllAttachmentsOnObjectDelete,
                    AttachmentLocation AS AttachmentLocation
             FROM Table_CustomAttachmentLocation
            ";

        #region Singleton

        public static IAttachmentLocationCache Instance { get => Nested.instance; }

        class Nested
        {
            protected Nested() { }
            static Nested() { }

            internal static readonly IAttachmentLocationCache instance = new AttachmentLocationCache();
        }

        #endregion
    }
}