﻿using Codeless.Framework.Sql.Standard;
using Codeless.Framework.LockManagement;
using Codeless.Server.Common.Cache.CacheBaseClasses;
using Codeless.Server.Common.Cache.CacheVersions;
using Codeless.Server.Common.ConnectionInformation;
using Codeless.Server.Interfaces.Common.Cache;
using System.Data;
using Codeless.Framework.DependencyInjection;
using Codeless.Framework.Logging.Standard;
using System;
using System.Collections.Generic;

namespace Codeless.Server.Common.Cache.CIC
{
    public class CICTypeCache : DictionaryCacheBase<(string Type, string Concern), CICType>, ICICTypeCache
    {
        private readonly ISqlWorkerFactory sqlWorkerFactory = new SqlWorkerFactory();
        private ISqlConnectionManager sqlConnectionManager;

        private Dictionary<string, List<string>> uniqueUserGroupsPerConcernForCICAdd = new Dictionary<string, List<string>>();
        private Dictionary<string, List<string>> uniqueUserGroupsPerConcernForCICCreate = new Dictionary<string, List<string>>();

        [InjectionMethod]
        public void Initialize(IPackageInfo packageInfo,
                               ICacheVersionsCache cacheVersionsCache,
                               ILockManagerFactory lockManagerFactory,
                               ILoggerFactory loggerFactory,
                               ISqlConnectionManager sqlConnectionManager)
        {

            base.Initialize(packageInfo, cacheVersionsCache, lockManagerFactory, loggerFactory, CacheVersionTypes.CICSetup);
            this.sqlConnectionManager = sqlConnectionManager ?? throw new ArgumentNullException(nameof(sqlConnectionManager));
        }

        public CICType GetCICType(string type, string concern)
        {
            if (type == null)
                throw new ArgumentNullException(nameof(type));

            if (concern == null)
                throw new ArgumentNullException(nameof(type));

            return GetCacheItem((type.Trim().ToLower(), concern.Trim()));
        }

        public List<string> GetAllUserGroupsCICAdd(string concern)
        {
            EnsureCorrectDataLoaded();
            return GetItemsForConcern(uniqueUserGroupsPerConcernForCICAdd, concern);
        }

        public List<string> GetAllUserGroupsCICCreate(string concern)
        {
            EnsureCorrectDataLoaded();
            return GetItemsForConcern(uniqueUserGroupsPerConcernForCICCreate, concern);
        }

        private List<string> GetItemsForConcern(Dictionary<string, List<string>> list, string concern)
        {
            if (concern == null)
                throw new ArgumentNullException(nameof(concern));

            if (list.TryGetValue(concern.ToLower(), out List<string> userGroups))
            {
                return userGroups;
            }
            else
            {
                return new List<string>();
            }
        }

        protected override void PreLoadData()
        {
            items.Clear();
            uniqueUserGroupsPerConcernForCICAdd.Clear();
            uniqueUserGroupsPerConcernForCICCreate.Clear();

            Dictionary<string, HashSet<string>> newUniqueUserGroupsPerConcernForCICAdd = new Dictionary<string, HashSet<string>>();
            Dictionary<string, HashSet<string>> newUniqueUserGroupsPerConcernForCICCreate = new Dictionary<string, HashSet<string>>();

            ConnectionOptions options = new ConnectionOptions()
            {
                Concern = Constants.ALL,
                UseReadOnlyDatabaseIfAvailable = true
            };

            using (IDbConnection connection = sqlConnectionManager.GetNewOpenedSqlConnection(options))
            {
                ISqlWorker worker = sqlWorkerFactory.GetSqlWorker(connection);

                foreach (CICType item in worker.ExecuteQuery<CICType>(queryGetInterfaceItemType))
                {
                    var key = (item.TypeName, item.ConcernId);
                    if (!items.ContainsKey(key))
                    {
                        items.Add(key, item);

                        if (string.IsNullOrWhiteSpace(item.ConcernId))
                        {
                            continue;
                        }

                        if (item.ExportAllowCICAddAPI && !string.IsNullOrWhiteSpace(item.ExportCICAddUserGroupName))
                        {
                            AddToHashSet(newUniqueUserGroupsPerConcernForCICAdd, item.ExportCICAddUserGroupName, item.ConcernId);
                        }

                        if (item.ExportAllowCICCreateAPI && !string.IsNullOrWhiteSpace(item.ExportCICCreateUserGroupName))
                        {
                            AddToHashSet(newUniqueUserGroupsPerConcernForCICCreate, item.ExportCICCreateUserGroupName, item.ConcernId);
                        }
                    }
                    else
                    {
                        logger.WriteWarning($"Duplicate CIC Type found '{item.TypeName}' found for concern {item.ConcernId}.");
                    }
                }

                AddUniqueResults(uniqueUserGroupsPerConcernForCICAdd, newUniqueUserGroupsPerConcernForCICAdd);
                AddUniqueResults(uniqueUserGroupsPerConcernForCICCreate, newUniqueUserGroupsPerConcernForCICCreate);
            }
        }

        private static void AddToHashSet(Dictionary<string, HashSet<string>> list, string userGroup, string concern)
        {
            if (!list.TryGetValue(concern.ToLower(), out HashSet<string> newUniqueUserGroups))
            {
                newUniqueUserGroups = new HashSet<string>();
                list.Add(concern.ToLower(), newUniqueUserGroups);
            }

            if (!newUniqueUserGroups.Contains(userGroup.ToLower()))
            {
                newUniqueUserGroups.Add(userGroup.ToLower());
            }
        }

        private void AddUniqueResults(Dictionary<string, List<string>> uniqueUserGroupsPerConcernForCICCreate, Dictionary<string, HashSet<string>> newUniqueUserGroupsPerConcernForCICCreate)
        {
            foreach (var concern in newUniqueUserGroupsPerConcernForCICCreate)
            {
                if (!uniqueUserGroupsPerConcernForCICCreate.TryGetValue(concern.Key, out List<string> userGroups))
                {
                    userGroups = new List<string>();
                    uniqueUserGroupsPerConcernForCICCreate.Add(concern.Key, userGroups);
                }

                userGroups.AddRange(concern.Value);
            }
        }

        private const string queryGetInterfaceItemType =
            @"SELECT LTRIM(RTRIM([InterfaceKindOf])) as InterfaceKindOf, 
                     [InterfaceIsOnCompanyCodeLevel],
                     [OneStepExportProcessingYn],
                     [OneStepImportProcessingYn],
                     [ClassIDFunction_UpdateDataFromInterfaceData],
                     [ProcessFunctionId_UpdateDataFromInterfaceData],
                     [TaskSchedulerId_UpdateDataFromInterfaceData],
                     LTRIM(RTRIM([XMLTypeNameImport])) as XMLTypeNameImport,
                     [XMLTypeVersionImport],
                     LTRIM(RTRIM([XMLTypeNameExport])) as XMLTypeNameExport,
                     [XMLTypeVersionExport],
                     LTRIM(RTRIM([ESB_WorkflowName])) as ESB_WorkflowName,
                     LTRIM(RTRIM([ESB_WorkflowAction])) as ESB_WorkflowAction,
                     [TaskSchedulerId_UpdateControlObject_Incoming] AS TaskSchedulerIdUpdateControlObject,
                     LTRIM(RTRIM([_pk_LocalSystemname])) AS LocalSystemName,
                     LTRIM(RTRIM([_pk_ConcernId])) AS ConcernId,
                     LOWER(LTRIM(RTRIM([_pk_InterfaceItemType]))) AS TypeName,
                     [Object_Id] as [ObjectId],
                     [ExportAsJson] as [ExportAsJson],
                     [ExportToFile] as [ExportToFile],
                     [ExportWebhookEvent] as [ExportWebhookEvent],
                     [_fk_ExportWebhookEventTypeId] as [ExportWebhookEventTypeId],
	                 [ExportAllowCICAddAPI] as ExportAllowCICAddAPI,
	                 [ExportAllowCICCreateAPI] as ExportAllowCICCreateAPI,
	                 LTRIM(RTRIM([_fk_ExportCICAddUserGroupName])) as ExportCICAddUserGroupName,
	                 LTRIM(RTRIM([_fk_ExportCICCreateUserGroupName])) as ExportCICCreateUserGroupName,
                     [ExportDataOnly] as ExportDataOnly,
                     [ExportCompression] as ExportCompression,
                     [StoreDataInsideCICRecord] as StoreDataInsideCICRecord,
                     [ExportToCurrentApplication] as ExportToCurrentApplication,
                     [IncomingPriority] as IncomingPriority,
                     LTRIM(RTRIM([IncomingConcernCode])) as IncomingConcernCode,
                     LTRIM(RTRIM([OutboundSubfolder])) as OutboundSubFolder
                FROM [Table_CIC_InterfaceItemType]";

        #region Singleton

        public static ICICTypeCache Instance
        {
            get
            {
                return Nested.instance;
            }
        }

        class Nested
        {
            protected Nested()
            { }
            static Nested()
            {
            }

            internal static readonly ICICTypeCache instance = new CICTypeCache();
        }

        #endregion

    }
}
