﻿namespace Codeless.Server.Common
{
    public static class RuntimeAttributeIds
    {
        public const int STATE = -1;

        public const int CacheVersion_CacheType = 85376;
        public const int CacheVersion_CacheVersion = 85377;

        public const int CacheVersionPersonal_CacheType = 87346;
        public const int CacheVersionPersonal_UserId = 87347;
        public const int CacheVersionPersonal_CacheVersion = 87348;

        public const int Counter_FullName = 87441;
        public const int Counter_Value = 87442;

        public const int PlugInMethodCall_PlugInName = 85361;
        public const int PlugInMethodCall_MethodName = 85362;
        public const int PlugInMethodCall_ParameterValue1 = 85363;
        public const int PlugInMethodCall_ParameterValue2 = 85364;
        public const int PlugInMethodCall_ParameterValue3 = 85365;
        public const int PlugInMethodCall_ParameterValue4 = 85366;
        public const int PlugInMethodCall_ParameterValue5 = 85367;
        public const int PlugInMethodCall_ParameterValue6 = 85368;
        public const int PlugInMethodCall_ParameterValue7 = 85369;
        public const int PlugInMethodCall_ParameterValue8 = 85370;
        public const int PlugInMethodCall_ParameterValue9 = 85371;
        public const int PlugInMethodCall_ParameterValue10 = 85372;
        public const int PlugInMethodCall_ReturnValue = 85373;
        public const int PlugInMethodCall_PlugInExecutionMode = 87742;
        public const int PlugInMethodCall_ParameterValue11 = 88343;
        public const int PlugInMethodCall_ParameterValue12 = 88344;
        public const int PlugInMethodCall_ParameterValue13 = 88345;
        public const int PlugInMethodCall_ParameterValue14 = 88346;
        public const int PlugInMethodCall_ParameterValue15 = 88347;

        public const int Proc_QueueItemStatus_Id = 89496;
        public const int Proc_QueueItemStatus_Status = 89546;
        public const int Proc_QueueItemStatus_ExecutionMessage = 89547;
        public const int Proc_QueueItemStatus_TaskId = 89584;
        public const int Proc_QueueItemStatus_TaskSchedulerId = 89585;
        public const int Proc_QueueItemStatus_TaskSchedulerName = 89586;
        public const int Proc_QueueItemStatus_TaskFunctionId = 89587;
        public const int Proc_QueueItemStatus_TaskFunctionName = 89588;
        public const int Proc_QueueItemStatus_TaskUserID = 89589;
        public const int Proc_QueueItemStatus_TaskCompany = 89590;
        public const int Proc_QueueItemStatus_TaskObjectIdToProcess = 90089;
        public const int Proc_QueueItemStatus_TaskConcern = 90090;
        public const int Proc_QueueItemStatus_StartTime = 90091;
        public const int Proc_QueueItemStatus_EndTime = 90092;
        public const int Proc_QueueItemStatus_QueuedTime = 90094;
        public const int Proc_QueueItemStatus_CallId = 90093;

        public const int Proc_ServiceCallStatus_CallId = 89541;
        public const int Proc_ServiceCallStatus_Status = 89549;
        public const int Proc_ServiceCallStatus_ExecutionMessage = 89548;

        public const int Proc_TaskScheduler_RunningQueueItems_TaskSchedulerId = 89531;
        public const int Proc_TaskScheduler_RunningQueueItems_TaskFunctionId = 89532;
        public const int Proc_TaskScheduler_RunningQueueItems_Concern = 89533;
        public const int Proc_TaskScheduler_RunningQueueItems_TaskScheduerIsBusy = 89534;

        public const int Proc_TaskSchedulerStatus_TaskSchedulerId = 89499;
        public const int Proc_TaskSchedulerStatus_TaskSchedulerStatus = 89550;

        public const int Proc_TaskStatus_TaskId = 89502;
        public const int Proc_TaskStatus_TaskNumberOfExecutionsLeft = 89518;
        public const int Proc_TaskStatus_QueueStatus = 89520;
        public const int Proc_TaskStatus_QueueItemId = 89521;
        public const int Proc_TaskStatus_QueueItemIsRunning = 89522;
        public const int Proc_TaskStatus_QueueItemIsSelectedByInitiator = 89523;
        public const int Proc_TaskStatus_QueueItemProcessThroughputInSeconds = 89524;
        public const int Proc_TaskStatus_QueueItemWaitInSeconds = 89525;
        public const int Proc_TaskStatus_QueueItemTaskExecutionMessage = 89526;
        public const int Proc_TaskStatus_QueueItemProcessTimeStart = 89527;
        public const int Proc_TaskStatus_QueueItemProcessTimeEnd = 89528;
        public const int Proc_TaskStatus_TaskStatus = 89553;
        public const int Proc_TaskStatus_DeterminedTaskStatus = 89517;
        public const int Proc_TaskStatus_QueueItemCount = 89555;

        public const int QueueItem_ProfilerAutoRollback = 89792;
        public const int QueueItem_ProfilerIncludeAttributeValues = 89793;
        public const int QueueItem_ProfilerEnabled = 89794;

        public const int ReportOption_CultureCode = 89633;

        public const int RuntimeInfo_Version = 89336;

        public const int Settings_PersistCICStatusUpdates = 87476;
        public const int Settings_PasswordStrengthMinLength = 87593;
        public const int Settings_PasswordStrengthMinCapitals = 87594;
        public const int Settings_PasswordStrengthMinNonCapital = 87595;
        public const int Settings_PasswordStrengthMinDigits = 87596;
        public const int Settings_PasswordStrengthMinSpecial = 87597;
        public const int Settings_PasswordStrengthSpecialCharacters = 87616;
        public const int Settings_UsePlugInToComputeQueueItemId = 88285;
        public const int Settings_DefaultRununit = 88372;
        public const int Settings_NewPasswordSimilarTestMinLevenshteinDistance = 88381;
        public const int Settings_PasswordHistoryDays = 88382;
        public const int Settings_PasswordMode = 88386;
        public const int Settings_CICProcessByTasks = 88455;
        public const int Settings_ProfilerCustomUIEnabled = 89311;
        public const int Settings_DoPlugInCallAuthorizationEnabled = 89315;
        public const int Settings_RestfulCallsAuthorizationEnabled = 89317;
        public const int Settings_ProfilerLinkCompany = 89340;
        public const int Settings_ProfilerLinkConcern = 89341;
        public const int Settings_ProfilerLinkModelerUrl1 = 89342;
        public const int Settings_DeleteObjectAttachmentsOptIn = 89491;
        public const int Settings_TaskInformationDeterminedByServer = 89493;
        public const int Settings_TaskUserIdForCIC = 89889;
        public const int Settings_CodeXVersionSupportsTaskSingleObjectId = 89933;
        public const int Settings_CodeXVersionSupportsTaskPkUsingSequence = 90076;

        public const int SystemSettingValue_AttributeID = 14304;
        public const int SystemSettingValue_Value = 14305;

        public const int Task_TaskId = 14382;
        public const int Task_UserId = 14383;
        public const int Task_Company = 14384;
        public const int Task_Rununit = 14385;
        public const int Task_FunctionId = 14386;
        public const int Task_SelectedObjects = 14388;
        public const int Task_TaskSchedulerId = 14390;
        public const int Task_TaskSchedulerName = 14399;
        public const int Task_Userdata = 14617;
        public const int Task_FunctionName = 14626;
        public const int Task_NoOfExecution = 84820;
        public const int Task_Priority = 84840;
        public const int Task_TaskDescription = 84841;
        public const int Task_ObjectSelection = 84849;
        public const int Task_NoOfExecutionLeft = 86753;
        public const int Task_ExternalQueueName = 88050;
        public const int Task_ExternalQueueServerBaseURL = 88052;
        public const int Task_ExternalQueueGuid = 89476;
        public const int Task_AllowRetryOnTransientError = 89611;
        public const int Task_ProfilerEnabled = 89789;
        public const int Task_ProfilerIncludeAttributeValues = 89790;
        public const int Task_ProfilerAutoRollback = 89791;
        public const int Task_SingleObjectId = 89932;
        public const int Task_ObjectNameToProcess = 90026;

        public const int TaskScheduler_IsActive = 86710;
        public const int TaskScheduler_TaskSchedulerID = 14394;
        public const int TaskScheduler_Name = 14395;
        public const int TaskScheduler_AllowRetryOnTransientError = 89610;
        public const int TaskScheduler_ExternalQueueGuid = 88009;
        public const int TaskScheduler_ExternalQueueName = 88010;
        public const int TaskScheduler_Type = 86523;
        public const int TaskScheduler_ExternalQueueWorkerCount = 88012;
        public const int TaskScheduler_TaskExecuterQueueGuid = 89473;
        public const int TaskScheduler_TaskExecuterQueueName = 89475;
        public const int TaskScheduler_TaskExecuterQueueWorkerCount = 89479;

        public const int User_Password = 14128;
        public const int User_Password2 = 14216;
        public const int User_PasswordOld = 14217;
        public const int User_Password1 = 14314;
        public const int User_PasswordLastModified = 85265;

        public const int XmlClass_FkName = 85494;

        public const int CurrentExternalDatabaseEnvironment = 87651;
    }
}
