﻿using Codeless.Framework.DependencyInjection;
using Codeless.Framework.Logging.Standard;
using Codeless.Server.Common.ClassesByApplication;
using Codeless.Server.Common.HandlerRedirect.DTO;
using Codeless.Server.Common.Helper;
using Codeless.Server.MetaClasses;
using System;
using System.Net;

namespace Codeless.Server.Common.HandlerRedirect
{
    public class HandlerRedirectBase : IHandlerRedirect
    {
        private IConfigSettings configSettings;
        private IClassesByApplicationProvider classesByApplicationProvider;
        private IMetaData metaData;
        private ILogger logger;
        private UrlResolver urlResolver;

        public virtual bool ShouldRedirect(long classId, out string applicationName)
        {
            applicationName = null;

            try
            {
                var classInfo = classesByApplicationProvider.GetClassById(Convert.ToInt32(classId));

                if (classInfo != null)
                {
                    applicationName = classInfo.ApplicationName;
                    return !classInfo.IsRuntime;
                }
            }
            catch (Exception ex)
            {
                logger.WriteError($"Error in redirecting class: {classId}", ex);
            }

            return false;
        }

        public virtual bool ShouldRedirectPlugin(string pluginName, out string applicationName)
        {
            try
            {
                if (configSettings.IsUnifiedClient && configSettings.IsMainServer)
                {
                    var plugins = metaData.Package.GetPlugInsRegistration();
                    var pluginInfo = plugins.Find(p => p.Name == pluginName);
                    applicationName = pluginInfo?.ApplicationRootName;

                    return (!string.IsNullOrEmpty(applicationName) && applicationName != configSettings.ApplicationName)
                        || (pluginInfo != null && pluginInfo.AvailableToAllAplications);
                }
            }
            catch (Exception ex)
            {
                logger.WriteError($"Error in redirecting plugin: {pluginName}", ex);
            }

            applicationName = configSettings.ApplicationName;
            return false;
        }

        public virtual Redirect CreateRedirectResponse(string redirectUrl, string requestMethod)
        {
            var statusCode = GetStatusCode(requestMethod);

            // Create a 301 - For GET requests
            // Create a 308 - For other verbs https://httpwg.org/specs/rfc9110.html#status.308 as per RFC 7538 to avoid changing the request method
            var redirect = new Redirect
            {
                StatusCode = statusCode,
                Location = urlResolver.ParseUrl(redirectUrl, configSettings.RootUrl)
            };

            return redirect;
        }

        private static int GetStatusCode(string requestMethod)
        {
            return string.Compare(requestMethod, "GET", StringComparison.OrdinalIgnoreCase) == 0
                ? (int)HttpStatusCode.MovedPermanently
#if NET
        : (int)HttpStatusCode.PermanentRedirect;
#else
        : 308;
#endif
        }

        [InjectionMethod]
        public void Initialize(IConfigSettings configSettings, IClassesByApplicationProvider classesByApplicationProvider, IMetaData metaData, ILoggerFactory loggerFactory)
        {
            this.configSettings = configSettings ?? throw new ArgumentNullException(nameof(configSettings));
            this.classesByApplicationProvider = classesByApplicationProvider ?? throw new ArgumentNullException(nameof(classesByApplicationProvider));
            this.metaData = metaData ?? throw new ArgumentNullException(nameof(metaData));
            _ = loggerFactory ?? throw new ArgumentNullException(nameof(loggerFactory));
            logger = loggerFactory.GetLogger(GetType());

            urlResolver = new UrlResolver(configSettings);
        }

        #region Singleton

        public static IHandlerRedirect Instance
        {
            get
            {
                return Nested.instance;
            }
        }

        class Nested
        {
            protected Nested()
            { }
            static Nested()
            { }

            internal static readonly IHandlerRedirect instance = new HandlerRedirectBase();
        }

        #endregion
    }
}
