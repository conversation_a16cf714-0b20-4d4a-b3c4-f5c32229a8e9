﻿using Codeless.Server.Common.Cache.CacheBaseClasses;
using System;
using System.Collections.Generic;

namespace Codeless.Server.Common.Cache.DataVersions
{
    public class DataVersionCacheInstance : DataVersionCache
    {
        private ObjectCache<(string concern, int classId), DataVersionWrapper> localCache;

        protected override void Initialize()
        {
            this.localCache = new ObjectCache<(string concern, int classId), DataVersionWrapper>(int.MaxValue, loggerFactory, lockManagerFactory);
        }

        protected override DataVersion GetVersionFromCache(string concern, int classId, bool assignConcern = false)
        {
            DataVersion version = null;
            DataVersionWrapper dataVersion;

            if (localCache.TryGet((concern, classId), out dataVersion))
            {
                version = new DataVersion(dataVersion.Version, dataVersion.TimeStamp);

                if(assignConcern) version.Concern = concern;
            }

            return version;
        }

        protected override DataVersion SetVersionToCache(string concern, int classId, DataVersion version, bool publishEvent = false)
        {           
            DataVersionWrapper dataVersion;
            DataVersion versionSet = null;

            if (localCache.TryGet((concern, classId), out dataVersion))
            {
                versionSet = UpdateVersion(dataVersion, version);
            }
            else
            {
                dataVersion = new DataVersionWrapper() { Version = version.Version, TimeStamp = version.TimeStamp };

                if (!localCache.TryAdd((concern, classId), dataVersion, 1) && localCache.TryGet((concern, classId), out dataVersion))
                {
                    versionSet = UpdateVersion(dataVersion, version);
                }
            }

            return versionSet;
        }

        private static DataVersion UpdateVersion(DataVersionWrapper dataVersionWrapper, DataVersion dataVersion)
        {
            DataVersion versionSet = null;

            // Check if version is newer if -> 1. version is higher, 2. if the version number started (again) at 1 
            if (dataVersionWrapper.Version < dataVersion.Version || dataVersion.Version == 1)
            {
                dataVersionWrapper.Version = dataVersion.Version;
                dataVersionWrapper.TimeStamp = dataVersion.TimeStamp;
                versionSet = dataVersion;
            }

            return versionSet;
        }
    }

    public class DataVersionWrapper : ICleanup
    {
        public int Version { get; set; }
        public DateTime TimeStamp { get; set; }

        public void Clean()
        {
            // no implementation needed
        }
    }
}
