﻿using System.Diagnostics.CodeAnalysis;

namespace Codeless.Server.Common.Cache.CustomMessages
{
    [ExcludeFromCodeCoverage]
    public class CustomMessage
    {
        public int MessageNumber { get; set; }

        public int MessageId { get; set; }

        public string Message { get; set; }

        public string FriendlyName { get; set; }

        public int LanguageId { get; set; }

        public int ClassId { get; set; }

        public int FunctionId { get; set; }

        public long ObjectId { get; set; }

        public int AttributeId { get; set; }

        public int InvariantId { get; set; }

        public int StateId { get; set; }

        public string CallName { get; set; }

        public int NavigationId { get; set; }

        public int AssociationId { get; set; }
        public int TransitionId { get; set; }   
    }
}
