﻿using Codeless.Server.Interfaces.Enumerations;

namespace Codeless.Server.Common.Enumerations
{
    public enum Token
    {
        ExternalQueueConnection,
        [StringValue(ConfigTokens.ServerBaseUrl)]
        ServerBaseUrl,
        [StringValue(ConfigTokens.ServerFiles)]
        ServerFiles,
        [StringValue(ConfigTokens.ModelFiles)]
        ModelFiles,
        [StringValue(ConfigTokens.EnvironmentName)]
        EnvironmentName,
        [StringValue(ConfigTokens.BaseUrl)]
        BaseUrl,
        [StringValue(ConfigTokens.RootUrl)]
        RootUrl,
        [StringValue(ConfigTokens.BaseUrlRegex)]
        BaseUrlRegex,
        [StringValue(ConfigTokens.ServerFilesHtml)]
        ServerFilesHtml,
        [StringValue(ConfigTokens.RabbitMqUrl)]
        RabbitMqUrl,
        [StringValue(ConfigTokens.PackageGuid)]
        PackageGuid,
        [StringValue(ConfigTokens.PackageName)]
        PackageName,
        [StringValue(ConfigTokens.PackageDescription)]
        PackageDescription,
        [StringValue(ConfigTokens.SmtpDomain)]
        SmtpDomain,
        [StringValue(ConfigTokens.SmtpPassword)]
        SmtpPassword,
        [StringValue(ConfigTokens.SmtpPort)]
        SmtpPort,
        [StringValue(ConfigTokens.SmtpServer)]
        SmtpServer,
        [StringValue(ConfigTokens.SmtpUser)]
        SmtpUser,
        [StringValue(ConfigTokens.SmtpUseSsl)]
        SmtpUseSsl,
        [StringValue(ConfigTokens.RootName)]
        RootName,
        [StringValue(ConfigTokens.TaskMonitorEnabled)]
        TaskMonitorEnabled,
        [StringValue(ConfigTokens.ApplicationName)]
        ApplicationName,
        [StringValue(ConfigTokens.ArtifactsLocation)]
        ArtifactsLocation,
        [StringValue(ConfigTokens.UnifiedApplicationName)]
        UnifiedApplicationName,
        [StringValue(FilterTokens.CurrentUser)]
        CurrentUser,
        [StringValue(FixScriptTokens.ConcernCode)]
        ConcernCode,
        CustomConfig,
        [StringValue(ConfigTokens.EnvironmentType)]
        EnvironmentType,
        [StringValue(ConfigTokens.IsDevelopmentEnvironment)]
        IsDevelopmentEnvironment,
    }
}
