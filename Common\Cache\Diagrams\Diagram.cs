﻿using System.Collections.Generic;

namespace Codeless.Server.Common.Cache.Diagrams
{
    public class Diagram
    {
        public int RootClassId { get; set; }
        public decimal DiagramId { get; set; }
        public string XMLTypeName { get; set; }
        public int XMLTypeVersion { get; set; }
        public string DiagramTypeName { get; set; }
        public DiagramType DiagramType { get; set; }
        public int FunctionId { get; set; }
        public string XmlNameSpace { get; set; }
        public string RootClassName { get; set; }
        public string XmlRootName { get; set; }

        public List<DiagramParameter> DiagramParameters { get; set; }
        public List<DiagramElementMapping> DiagramElementMappings { get; set; }

    }

    public class DiagramParameter
    {
        public string Name { get; set; }
        public decimal ParameterId { get; set; }
        public int AttributeId { get; set; }
        public string AttributeName { get; set; }
        public int XMLParameterId { get; set; }
    }

    public class DiagramElementMapping
    {
        public decimal ElementMappingId { get; set; }
        public int FunctionIdSubListWindow { get; set; }
        public int FunctionIdPropertiesWindow { get; set; }
        public int NavigationIdProperties { get; set; }
        public int NavigationIdSubList { get; set; }
        public string NavigationParametersProperties { get; set; }
        public string NavigationParametersSubList { get; set; }
        public int ScreenIdPropertiesWindow { get; set; }
        public int ScreenIdSubListWindow { get; set; }
        public string ElementName { get; set; }
        public DiagramElement Element { get; set; }
        public int ClassId { get; set; }
        public int ConnectorClassIdFrom { get; set; }
        public int ConnectorClassIdTo { get; set; }
        public bool IsAddableInUI { get; set; }
        public bool IsChangeableInUI { get; set; }
        public bool IsDeletableInUI { get; set; }
        public bool IsListOfObjects { get; set; }
        public string MappingPath { get; set; }
        public string ClassName { get; set; }
        public string XmlNodeName { get; set; }
        public string XmlListNodeName { get; set; }
        public string XmlDeleteNodeName { get; set; }
        public int ToolbarGroupOrder { get; set; }
        public bool AutoSaveAdd { get; set; }

        public List<DiagramElementPropertyMapping> DiagramPropertiesMapping { get; set; }
    }

    public class DiagramElementPropertyMapping
    {
        public decimal PropertyMappingId { get; set; }
        public int AttributeId { get; set; }
        public string AttributeName { get; set; }
        public string PropertyName { get; set; }
        public DiagramElementProperty DiagramElementProperty { get; set; }
        public string MappingPath { get; set; }
        public bool IsChangeableInUI { get; set; }
        public string XmlNodeName { get; set; }
    }
}
