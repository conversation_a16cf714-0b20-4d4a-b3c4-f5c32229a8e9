﻿using Codeless.Framework.DependencyInjection;
using Codeless.Framework.LockManagement;
using Codeless.Framework.Logging.Standard;
using Codeless.Framework.Sql.Standard;
using Codeless.Server.Common.Cache.CacheBaseClasses;
using Codeless.Server.Common.Cache.CacheVersions;
using Codeless.Server.Common.ConnectionInformation;
using Codeless.Server.Common.Exceptions;
using Codeless.Server.Interfaces.Common.Cache;
using System;
using System.Data;
using System.Data.SqlClient;
using System.Linq;

namespace Codeless.Server.Common.Cache.Diagrams
{
    public class DiagramCache : DictionaryJITLoadCacheBase<decimal, Diagram>, IDiagramCache
    {
        private ISqlWorkerFactory sqlWorkerFactory;
        private ISqlConnectionManager sqlConnectionManager;
        private IDiagramTypeCache diagramTypeCache;

        [InjectionMethod]
        public void Initialize(
            ISqlWorkerFactory sqlWorkerFactory,
            ISqlConnectionManager sqlConnectionManager,
            IPackageInfo packageInfo,
            ICacheVersionsCache cacheVersionsCache,
            ILockManagerFactory lockManagerFactory,
            ILoggerFactory loggerFactory,
            IDiagramTypeCache diagramTypeCache)
        {
            Initialize(
                packageInfo,
                cacheVersionsCache,
                lockManagerFactory,
                loggerFactory,
                CacheVersionTypes.Diagram);

            this.sqlWorkerFactory = sqlWorkerFactory ?? throw new ArgumentNullException(nameof(sqlWorkerFactory));
            this.sqlConnectionManager = sqlConnectionManager ?? throw new ArgumentNullException(nameof(sqlConnectionManager));
            this.diagramTypeCache = diagramTypeCache ?? throw new ArgumentNullException(nameof(diagramTypeCache));
        }

        public Diagram GetDiagram(decimal diagramId)
        {
            return GetCacheItem(diagramId);
        }

        protected override Diagram LoadDataItem(decimal key)
        {
            using (IDbConnection connection = sqlConnectionManager.GetNewOpenedSqlConnection(Constants.ALL, false))
            {
                ISqlWorker sqlWorker = sqlWorkerFactory.GetSqlWorker(connection);
                Diagram result = null;

                try
                {
                    result = sqlWorker.ExecuteQuery<Diagram>(queryGetDiagram, new SqlParameter("@DiagramId", key)).First();

                    result.DiagramParameters = sqlWorker.ExecuteQuery<DiagramParameter>(queryGetParameters, new SqlParameter("@DiagramId", key)).ToList();

                    result.DiagramElementMappings = sqlWorker.ExecuteQuery<DiagramElementMapping>(queryGetElementMappings, new SqlParameter("@DiagramId", key)).ToList();

                    result.DiagramType = diagramTypeCache.GetDiagramType(result.DiagramTypeName);

                    foreach (DiagramElementMapping elementMapping in result.DiagramElementMappings)
                    {
                        elementMapping.DiagramPropertiesMapping = sqlWorker.ExecuteQuery<DiagramElementPropertyMapping>(queryGetPropertyMappings,
                            new SqlParameter("@DiagramId", key),
                            new SqlParameter("@ElementMappingId", elementMapping.ElementMappingId)).ToList();

                        elementMapping.Element = result.DiagramType.DiagramElements.Single(f => f.Name == elementMapping.ElementName);

                        foreach (DiagramElementPropertyMapping propertyMapping in elementMapping.DiagramPropertiesMapping)
                        {
                            propertyMapping.DiagramElementProperty = elementMapping.Element.ElementProperties.Single(f => f.Name == propertyMapping.PropertyName);
                        }
                    }
                }
                catch (Exception ex)
                {
                    throw new LoadDataException($"Error loading diagram type {key}.", ex);
                }

                return result;
            }
        }

        private static string queryGetDiagram = @"
            SELECT 
                _fk_RootClassId as RootClassId, 
                _pk_DiagramId as DiagramId, 
                LTRIM(RTRIM(_fk_XMLTypeName)) as XMLTypeName,  
                _fk_XMLTypeVersion as XMLTypeVersion,
                LTRIM(RTRIM(_fk_DiagramType)) as DiagramTypeName, 
                _fk_ReadFunctionId as FunctionId, 
                XmlNameSpace, 
                LTRIM(RTRIM(RootClassName)) as RootClassName, 
                RTRIM(LTRIM(XmlRootName)) as XmlRootName 
            FROM Table_Diagram 
            WHERE _pk_DiagramId = @DiagramId
        ";

        private static string queryGetParameters = @"
            SELECT 
                _pk_ParameterId as ParameterId, 
                LTRIM(RTRIM(ParameterName)) as Name, 
                _fk_AttributeId as AttributeId, 
                LTRIM(RTRIM(AttributeName)) as AttributeName,
                XMLParameterId 
            FROM Table_DiagramParameter 
            WHERE _fk_DiagramId = @DiagramId
        ";

        private static string queryGetElementMappings = @"
            SELECT 
                _pk_ElementMappingId as ElementMappingId, 
                _fk_FunctionIdSubListW as FunctionIdSubListWindow,
                _fk_FunctionIdPropertiesW as FunctionIdPropertiesWindow,
                ScreenIdPropertiesW as ScreenIdPropertiesWindow, 
                ScreenIdSubListW as ScreenIdSubListWindow, 
                _fk_DiagramElementName as ElementName,
                NavigationIdProperties, 
                NavigationIdSubList, 
                AutoSaveAdd, 
                ToolbarGroupOrder, 
                _fk_ClassId as ClassId, 
                _fk_ConnectorClassIdFrom as ConnectorClassIdFrom, 
                _fk_ConnectorClassIdTo as ConnectorClassIdTo,
                IsAddableInUI, 
                IsChangeableInUi, 
                IsDeletableInUi, 
                IsListOfObjects, 
                MappingPath, 
                RTRIM(LTRIM(ClassName)) as ClassName, 
                RTRIM(LTRIM(XmlNodeName)) as XmlNodeName, 
                RTRIM(LTRIM(XmlListNodeName)) as XmlListNodeName, 
                RTRIM(LTRIM(XmlDeleteNodeName)) as XmlDeleteNodeName 
            FROM Table_DiagramElementMapping
            WHERE _fk_DiagramId = @DiagramId
        ";

        private static string queryGetPropertyMappings = @"
            SELECT 
                _pk_PropertyMappingId as PropertyMappingId, 
                AttributeId, 
                IsChangeableInUi, 
                MappingPath,
                LTRIM(RTRIM(NameInModel)) as AttributeName, 
                _fk_DiagramElementProperty as PropertyName, 
                RTRIM(LTRIM(XmlNodeName)) as XmlNodeName 
            FROM Table_DiagramElementPropertyMapping 
            WHERE 
                _fk_DiagramId = @DiagramId AND 
                _fk_DiagramElementMapping = @ElementMappingId
        ";

        #region Singleton

        public static IDiagramCache Instance
        {
            get
            {
                return Nested.instance;
            }
        }

        class Nested
        {
            protected Nested()
            { }
            static Nested()
            {
            }

            internal static readonly IDiagramCache instance = new DiagramCache();
        }

        #endregion

    }
}
