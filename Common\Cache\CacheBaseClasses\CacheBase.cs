﻿using Codeless.Framework.Logging.Standard;
using Codeless.Framework.LockManagement;
using Codeless.Server.Common.Cache.CacheVersions;
using Codeless.Server.Common.ConnectionInformation;
using Codeless.Server.Interfaces.Common.Cache;
using System;

namespace Codeless.Server.Common.Cache.CacheBaseClasses
{
    /// <summary>
    /// Base class for caches, only inherit directly from here to create a new cache type.
    /// Cache is default flushed based on the cache version type passed into the constructor.
    /// To customize the cache flushing: do not pass in a cache version type with constructor and over the NeedsUpdating and SetLoadedVersion methods.
    /// </summary>
    public abstract class CacheBase : ICache
    {
        // Dependencies
        protected ICacheVersionsCache cacheVersionsCache;
        protected ILockManager lockManager;
        protected ILogger logger;
        protected IPackage package;

        protected CacheVersionTypes? cacheVersionType = null;
        protected string loadedVersion;

        protected void Initialize(IPackageInfo packageInfo, 
                                  ICacheVersionsCache cacheVersionsCache, 
                                  ILockManagerFactory lockManagerFactory, 
                                  ILoggerFactory loggerFactory, 
                                  CacheVersionTypes cacheVersionType)
        {
            this.cacheVersionType = cacheVersionType;
            this.Initialize(packageInfo, cacheVersionsCache, lockManagerFactory, loggerFactory);
        }

        protected void Initialize(IPackageInfo packageInfo,
                                  ICacheVersionsCache cacheVersionsCache,
                                  ILockManagerFactory lockManagerFactory,
                                  ILoggerFactory loggerFactory)
        {
            packageInfo = packageInfo ?? throw new ArgumentNullException(nameof(packageInfo));
            this.cacheVersionsCache = cacheVersionsCache ?? throw new ArgumentNullException(nameof(cacheVersionsCache));
            lockManagerFactory = lockManagerFactory ?? throw new ArgumentNullException(nameof(lockManagerFactory));
            loggerFactory = loggerFactory ?? throw new ArgumentNullException(nameof(loggerFactory));

            this.lockManager = lockManagerFactory.GetLockManager(this.GetType());
            this.logger = loggerFactory.GetLogger(this.GetType());
            this.loadedVersion = null;
            this.package = packageInfo.GetDefaultPackage();
        }

        public abstract int Count();

        public abstract void Flush();

        protected abstract void PreLoadData();

        protected void EnsureCorrectDataLoaded()
        {
            bool needsUpdate = false;

            this.lockManager.LockReadMode(() =>
            {
                needsUpdate = this.NeedsUpdating();
            });

            if (needsUpdate)
            {
                this.lockManager.LockWriteMode(() =>
                {
                    if (this.NeedsUpdating())
                    {
                        Flush();
                        PreLoadData();
                        SetLoadedVersion();
                    }
                });
            }
        }

        protected virtual void SetLoadedVersion()
        {
            if (cacheVersionType.HasValue)
            {
                if (cacheVersionType.Value == CacheVersionTypes.Metadata)
                {
                    this.loadedVersion = cacheVersionsCache.GetMetadataVersion().ToString();
                }
                else
                {
                    this.loadedVersion = cacheVersionsCache.GetCacheVersion(cacheVersionType.Value).ToString();
                }
            }
        }

        protected virtual bool NeedsUpdating()
        {
            bool needsUpdate = false;

            if (cacheVersionType.HasValue)
            {
                string newCacheVersion;

                if (cacheVersionType.Value == CacheVersionTypes.Metadata)
                {
                    newCacheVersion = cacheVersionsCache.GetMetadataVersion().ToString();
                }
                else
                {
                    newCacheVersion = cacheVersionsCache.GetCacheVersion(cacheVersionType.Value).ToString();
                }

                needsUpdate = string.IsNullOrEmpty(loadedVersion) || loadedVersion != newCacheVersion;
            }

            return needsUpdate;
        }
    }
}