﻿using Codeless.Framework.EventProcessing.Event;
using Codeless.Framework.EventProcessing.Event.Contracts.Versions;
using Codeless.Framework.EventProcessing.EventHandling;
using Codeless.Server.Common.Cache.CacheVersions;
using System;

namespace Codeless.Server.Common.EventStore.Consume.EventHandler
{
    public class PersonalCacheVersionsHandler : HandlerBase<PersonalCacheVersionIncreased>
    {
        public PersonalCacheVersionsHandler(ModelContainer modelContainer) : base(modelContainer)
        {
        }

        protected override void ProcessEvent(PersonalCacheVersionIncreased eventObject, DateTime eventTime, RelatedEvent relatedEvent)
        {
            CacheVersionsCache.Instance.UpdateCacheVersionPersonal(eventObject.UserName, eventObject.CacheType, eventObject.Version, false);
        }
    }
}
