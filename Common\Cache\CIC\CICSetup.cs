﻿namespace Codeless.Server.Common.Cache.CIC
{
    public class CICSetup
    {
        public string ConcernId { get; set; }
        public string LocalSystemName { get; set; }
        public string PathFolderName { get; set; }
        public string EsbSystemName { get; set; }
        public bool FileScanEnabled { get; set; }
        public bool AllowAPICalls { get; set; }        
        public int TaskSchedulerIdIncoming { get; set; }
        public int ProcessFunctionIdIncoming { get; set; }
    }
}
