﻿namespace Codeless.Server.Common
{
    public static class RuntimeClassIds
    {
        public const int Function = 1109;
        public const int User = 1110;
        public const int Language = 1114;
        public const int Class = 1161;
        public const int SystemSettingValue = 1196;
        public const int SystemSetting = 1197;
        public const int ReportOption = 1232;
        public const int Printer = 1233;
        public const int EmailAddress = 1234;
        public const int Task = 1235;
        public const int TaskScheduler = 1236;
        public const int WorkFlowUserSetting = 1248;
        public const int AvailableReport = 1269;
        public const int State = 3841;
        public const int Attribute = 3842;
        public const int UserProfile = 5118;
        public const int TaskObject = 5157;
        public const int PlugInMethodCall = 5163;
        public const int CacheVersion = 5164;
        public const int ClassRelation = 5165;
        public const int XmlAttribute = 5170;
        public const int XmlClass = 5171;
        public const int XmlParameter = 5172;
        public const int XmlType = 5173;
        public const int XmlTypeVersion = 5174;
        public const int CICTask = 5181;
        public const int QueueItem = 5239;
        public const int Profiler = 5251;
        public const int EffectiveAccessRights = 5258;
        public const int ExcelExportTemplate = 5263;
        public const int ExcelImport = 5265;
        public const int List = 5272;
        public const int ListColumn = 5273;
        public const int ListFilter = 5274;
        public const int ListTranslation = 5277;
        public const int Comment = 5293;
        public const int GeneratedReport = 5296;
        public const int IndexedClass = 5297;
        public const int SystemMenuVersion = 5300;
        public const int UserListSettings = 5302;
        public const int UserSettings = 5303;
        public const int Counter = 5309;
        public const int ListSettings = 5313;
        public const int DataTracking = 5332;
        public const int WordMergeTemplate = 5348;
        public const int UIRecording = 5351;
        public const int PasswordHistory = 5369;
        public const int RuntimeInfo = 5503;
        public const int CacheVersionPersonal = 5292;
        public const int AutoCorrectAttributes = 5291;
        public const int Proc_QueueItemStatus = 5514;
        public const int Proc_TaskSchedulerStatus = 5515;
        public const int Proc_TaskStatus = 5516;
        public const int Proc_TaskScheduler_RunningQueueItems = 5518;
        public const int LogEntries = 5323;
        public const int CICInterfaceControl = 5178;
    }
}
