﻿using System;
using System.Runtime.Serialization;

namespace Codeless.Server.Common.Exceptions
{
    [Serializable]
    public class RedisFailureException : Exception
    {
        public RedisFailureException(string message, Exception innerException)
           : base(message, innerException)
        {
        }

        public RedisFailureException(string message)
         : base(message)
        {
        }

        protected RedisFailureException(SerializationInfo info, StreamingContext context)
          : base(info, context)
        {
        }

        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            base.GetObjectData(info, context);
        }
    }
}
