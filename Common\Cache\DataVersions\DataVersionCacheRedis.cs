﻿using Codeless.Framework.EventProcessing.Event.Contracts.Versions;
using Codeless.Server.Common.ConnectionInformation;
using Codeless.Server.Common.JsonConverters;
using StackExchange.Redis;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Codeless.Server.Common.Cache.DataVersions
{
    public class DataVersionCacheRedis : DataVersionCache
    {
        private RedisCacheManager cache;
        private readonly RedisUsageType usageType = RedisUsageType.DataVersions;

        private string keyPrefix;

        protected override void Initialize()
        {
            this.cache = RedisCacheManager.Instance;
            keyPrefix = PackageInfo.Instance.GetDefaultPackage().PackageName + ".DataVersion";
        }

        protected override DataVersion GetVersionFromCache(string concern, int classId, bool assignConcern = false)
        {
            return GetVersion(concern, classId);
        }

        protected override Dictionary<int, DataVersion> GetVersionListFromCache(string concern, IEnumerable<int> classIds, bool assignConcern = false)
        {
            List<(int classId, string concern, string data)> dataList = GetRedisValuesList(concern, classIds);

            if (dataList == null || !dataList.Any())
            {
                return new Dictionary<int, DataVersion>();
            }

            ConcurrentDictionary<int, DataVersion> dataVersions = DeserializeRedisData(dataList, assignConcern);
            Dictionary<int, DataVersion> result = dataVersions.ToDictionary(x => x.Key, x => x.Value);

            return result;
        }

        private ConcurrentDictionary<int, DataVersion> DeserializeRedisData(List<(int classId, string concern, string data)> dataList, bool assignConcern = false)
        {
            ConcurrentDictionary<int, DataVersion> localDataVersions = new ConcurrentDictionary<int, DataVersion>();

            Parallel.ForEach(dataList, (item) =>
            {
                if (!string.IsNullOrEmpty(item.data))
                {
                    DataVersion cachedVersion = GetCachedVersion(item.data, item.concern, item.classId, assignConcern);

                    if (cachedVersion != null)
                        localDataVersions.TryAdd(item.classId, cachedVersion);
                }
            });

            return localDataVersions;
        }

        private List<(int classId, string concern, string data)> GetRedisValuesList(string concern, IEnumerable<int> classIds)
        {
            string[] dataList = GetRedisValues(concern, classIds);

            List<int> classIdList = classIds.ToList();

            List<(int classId, string concern, string data)> dataItemWithClassId = new List<(int classId, string concern, string data)>();
            int classIdIndex = 0;

            for (int i = 0; i < dataList.Length; i++)
            {
                bool allConcerns = i % 2 == 0;

                if (allConcerns)
                {
                    dataItemWithClassId.Add((classIdList[classIdIndex], "*", dataList[i]));
                }
                else
                {
                    dataItemWithClassId.Add((classIdList[classIdIndex], concern, dataList[i]));
                    classIdIndex++;
                }
            }

            return dataItemWithClassId;
        }

        private string[] GetRedisValues(string concern, IEnumerable<int> classIdList)
        {
            List<string> keys = new List<string>();

            foreach (int classId in classIdList)
            {
                var classConcern = GetClassConcernForDataVersion(concern, classId);
                keys.Add(GetKey("*", classId));
                keys.Add(GetKey(classConcern, classId));
            }

            string[] dataList = this.cache.StringGet(keys.ToArray(), usageType);

            return dataList;
        }

        private DataVersion GetCachedVersion(RedisValue data, string concern, int classId, bool assignConcern = false)
        {
            try
            {
                var version = RedisSerializer.Deserialize<DataVersion>(data);

                if (assignConcern) version.Concern = concern;

                return version;
            }
            catch (Exception ex)
            {
                logger.WriteError(string.Format("Failed to deserialize class data version for class {0}, concern {1}.", classId, concern), ex);
            }

            return null;
        }

        protected override DataVersion SetVersionToCache(string concern, int classId, DataVersion version, bool publishEvent = false)
        {
            DataVersion setVersion = null;

            if (this.cache.StringSet(GetKey(concern, classId), RedisSerializer.Serialize(version), usageType))
            {
                setVersion = version;
            }

            return setVersion;
        }

        private string GetKey(string concern, int classId)
        {
            return string.Format("{0}.{1}.{2}", keyPrefix, concern, classId);
        }

        private void Remove(string concern, int classId)
        {
            try
            {
                this.cache.KeyDelete(GetKey(concern, classId));
            }
            catch (Exception ex)
            {
                logger.WriteWarning(string.Format("Failed to remove cache entry '{0}-{1}' from cache.", concern, classId), ex);
            }
        }

        private DataVersion GetVersion(string concern, int classId, bool assignConcern = false)
        {
            DataVersion response = null;

            string data = this.cache.StringGet(GetKey(concern, classId), usageType);

            if (!string.IsNullOrWhiteSpace(data))
            {
                try
                {
                    response = RedisSerializer.Deserialize<DataVersion>(data);

                    if (assignConcern) response.Concern = concern;
                }
                catch (Exception ex)
                {
                    logger.WriteWarning(string.Format("Failed to deserialize cache entry '{0}-{1}' to DataVersion object.", concern, classId), ex);

                    Remove(concern, classId);
                }
            }

            return response;
        }
    }
}