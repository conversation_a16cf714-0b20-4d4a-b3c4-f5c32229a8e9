﻿using Codeless.Framework.DependencyInjection;
using Codeless.Framework.Logging.Standard;
using Codeless.Framework.Sql.Standard;
using Codeless.Framework.LockManagement;
using Codeless.Server.Common.Cache.CacheBaseClasses;
using Codeless.Server.Common.Cache.CacheVersions;
using Codeless.Server.Common.ConnectionInformation;
using Codeless.Server.Common.Enumerations;
using Codeless.Server.Interfaces.Common.Cache;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;

namespace Codeless.Server.Common.Cache.CustomMessages
{
    public class CustomMessagesCache : DictionaryCacheBase<int, IQueryable<CustomMessage>>, ICustomMessagesCache
    {
        private ISqlConnectionManager sqlConnectionManager;
        private ISqlWorkerFactory sqlWorkerFactory;
        private ITokenHelper tokenHelper;

        [InjectionMethod]
        public void Initialize(IPackageInfo packageInfo,
            ICacheVersionsCache cacheVersionsCache,
            ILockManagerFactory lockManagerFactory,
            ILoggerFactory loggerFactory,
            ISqlConnectionManager sqlConnectionManager,
            ISqlWorkerFactory sqlWorkerFactory,
            ITokenHelper tokenHelper)
        {
            this.sqlConnectionManager = sqlConnectionManager ?? throw new ArgumentNullException(nameof(sqlConnectionManager));
            this.sqlWorkerFactory = sqlWorkerFactory ?? throw new ArgumentNullException(nameof(sqlWorkerFactory));
            this.tokenHelper = tokenHelper ?? throw new ArgumentNullException(nameof(tokenHelper));

            base.Initialize(packageInfo, cacheVersionsCache, lockManagerFactory, loggerFactory, CacheVersionTypes.CustomMessages);
        }

        public bool TryGetCustomMessage(CustomMessageKey key, out CustomMessage customMessage)
        {
            customMessage = null;

            if (!TryGetCacheItem(key.MessageNumber, out IQueryable<CustomMessage> messagesList)) return false;

            messagesList = key.LanguageId == null ? messagesList : messagesList.Where(m => m.LanguageId == key.LanguageId);
            messagesList = key.ClassId == null ? messagesList : messagesList.Where(m => m.ClassId == key.ClassId);
            messagesList = key.MessageId == null ? messagesList : messagesList.Where(m => m.MessageId == key.MessageId);
            messagesList = key.FunctionId == null ? messagesList : messagesList.Where(m => m.FunctionId == key.FunctionId);
            messagesList = key.InvariantId == null ? messagesList : messagesList.Where(m => m.InvariantId == key.InvariantId);
            messagesList = key.StateId == null ? messagesList : messagesList.Where(m => m.StateId == key.StateId);
            messagesList = key.CallName == null ? messagesList : messagesList.Where(m => key.CallName.Equals(m.CallName, StringComparison.InvariantCultureIgnoreCase));
            messagesList = key.AssociationId == null ? messagesList : messagesList.Where(m => m.AssociationId == key.AssociationId);
            messagesList = key.NavigationId == null ? messagesList : messagesList.Where(m => m.NavigationId == key.NavigationId);
            messagesList = key.AttributeId == null ? messagesList : messagesList.Where(m => m.AttributeId == key.AttributeId);
            messagesList = key.TransitionId == null ? messagesList : messagesList.Where(m => m.TransitionId == key.TransitionId);

            customMessage = messagesList.FirstOrDefault();

            return customMessage != null;
        }

        protected override void PreLoadData()
        {
            items.Clear();

            List<CustomMessage> messages = null;

            using (IDbConnection connection = sqlConnectionManager.GetNewOpenedSqlConnection(Constants.ALL, false))
            {
                ISqlWorker worker = sqlWorkerFactory.GetSqlWorker(connection);
                messages = worker.ExecuteQuery<CustomMessage>(queryGetCustomMessageList).ToList();
            }

            if (messages?.Count > 0)
            {
                Dictionary<int, List<CustomMessage>> dictionary = new Dictionary<int, List<CustomMessage>>();

                messages.ForEach(customMessage =>
                {
                    customMessage.Message = tokenHelper.Replace(customMessage.Message, Token.RootUrl);

                    if (dictionary.TryGetValue(customMessage.MessageNumber, out List<CustomMessage> messagesList))
                        messagesList.Add(customMessage);
                    else
                        dictionary.Add(customMessage.MessageNumber, new List<CustomMessage>() { customMessage });
                });

                foreach (var item in dictionary)
                {
                    items.Add(item.Key, item.Value.AsQueryable());
                }
            }
        }

        private static readonly string queryGetCustomMessageList = @"
SELECT   [OBJECT_ID]                    AS ObjectId
        ,[FRIENDLY_NAME]                AS FriendlyName
        ,[_pk_AssociationId]            AS AssociationId
        ,LTRIM(RTRIM([_pk_CallName]))   AS CallName
        ,[_pk_ClassId]                  AS ClassId
        ,[_pk_FunctionId]               AS FunctionId
        ,[_pk_AttributeId]              AS AttributeId
        ,[_pk_InvariantId]              AS InvariantId
        ,[_pk_Language]                 AS LanguageId
        ,[_pk_MessageId]                AS MessageId
        ,[_pk_MessageNumber]            AS MessageNumber
        ,[_pk_NavigationId]             AS NavigationId
        ,[_pk_StateId]                  AS StateId
        ,[Message]                      AS Message
        ,[_pk_TransitionId]             AS TransitionId
FROM    [dbo].[Table_CustomMessage]";

        #region Singleton

        private CustomMessagesCache() { }

        public static ICustomMessagesCache Instance
        {
            get
            {
                return Nested.instance;
            }
        }

        class Nested
        {
            protected Nested()
            { }

            static Nested()
            {
            }

            internal static readonly ICustomMessagesCache instance = new CustomMessagesCache();
        }

        #endregion
    }
}
