﻿using Codeless.Framework.EventProcessing.Event;
using Codeless.Framework.EventProcessing.Event.Contracts.Versions;
using Codeless.Framework.EventProcessing.EventHandling;
using Codeless.Server.Common.Cache.CacheVersions;
using Codeless.Server.Common.Helper;
using Codeless.Server.Interfaces.Common.Cache;
using System;

namespace Codeless.Server.Common.EventStore.Consume.EventHandler
{
    public class CacheVersionsHandler : HandlerBase<CacheVersionIncreased>
    {
        private readonly ModelContainer modelContainerRef;

        public CacheVersionsHandler(ModelContainer modelContainer) : base(modelContainer)
        {
            this.modelContainerRef = modelContainer;
        }

        protected override void ProcessEvent(CacheVersionIncreased eventObject, DateTime eventTime, RelatedEvent relatedEvent)
        {
            if (ShouldSkipProcessing(eventObject))
            {
                return;
            }

            CacheVersionsCache.Instance.UpdateCacheVersion(eventObject.CacheType, eventObject.Version, false);

            if (eventObject.CacheType == CacheVersionTypes.Metadata)
            {
                CacheVersionUtilities.Instance.HandleCacheVersionIncreased(eventObject.Version);
            }
        }

        private bool ShouldSkipProcessing(CacheVersionIncreased eventObject)
        {
            return eventObject.CacheType == CacheVersionTypes.Metadata
                   && ConfigSettings.Instance.UseEventStoreCacheVersions
                   && modelContainerRef.IsLeader;
        }
    }
}