﻿using Codeless.Server.Interfaces;

namespace Codeless.Server.Common.Identification
{
    /// <summary>
    /// Normal ObjectId is only unique with the class, the global objectId is unique over the entire database.
    /// </summary>
    public class GlobalObjectId : TupleWrapper<(long objectId, int classId, string concern)>, IGlobalObjectId
    {
        public GlobalObjectId(long objectId, int classId, string concern) 
            : base((objectId, classId, concern))
        {
        }

        public long ObjectId => key.objectId;
        public int ClassId => key.classId;
        public string Concern => key.concern;
    }
}