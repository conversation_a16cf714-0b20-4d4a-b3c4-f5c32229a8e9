﻿using Codeless.Server.Common.MslDataContracts;
using System;

namespace Codeless.Server.Common.Helper
{
    public static class DateTimeHelper
    {
        public static DateTime ConvertTimeZone(DateTime value)
        {
            DateTime result = DateTime.SpecifyKind(value, DateTimeKind.Unspecified);

            result = new DateTime(result.Year, result.Month, result.Day, result.Hour, result.Minute, result.Second, 0, result.Kind);

            return result;
        }

        public static DateTime ConvertToUTCDateTime(DateTime dateTime)
        {
            TimeZoneInfo localTimeZone = TimeZoneInfo.Local;
            return TimeZoneInfo.ConvertTimeToUtc(dateTime, localTimeZone);
        }
    }
}
