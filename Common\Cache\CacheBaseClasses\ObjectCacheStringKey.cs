﻿using Codeless.Framework.Logging.Standard;
using Codeless.Framework.LockManagement;
using System;

namespace Codeless.Server.Common.Cache.CacheBaseClasses
{
    /// <summary>
    /// Extension of ObjectCache, used when the key is of type string and formatting the key is intended.
    /// </summary>
    /// <typeparam name="ContentType"></typeparam>
    public class ObjectCacheStringKey<ContentType> : ObjectCache<string, ContentType> where ContentType : class
    {
        public ObjectCacheStringKey(int maxSize, ILoggerFactory loggerfactory, ILockManagerFactory lockManagerFactory) : base(maxSize, loggerfactory, lockManagerFactory) { }

        public ObjectCacheStringKey(TimeSpan ttl, ILoggerFactory loggerfactory, ILockManagerFactory lockManagerFactory) : base(ttl, loggerfactory, lockManagerFactory) { }

        public override bool TryAdd(string key, ContentType content, int size)
        {
            return base.TryAdd(FormatKey(key), content, size);
        }

        public override bool IsKnownKey(string key)
        {
            return base.IsKnownKey(FormatKey(key));
        }

        public override bool TryGet(string key, out ContentType content)
        {
            return base.TryGet(FormatKey(key), out content);
        }

        public override ContentType TryRemove(string key, out int? size)
        {
            return base.TryRemove(FormatKey(key), out size);
        }

        private static string FormatKey(string key)
        {
            return key.ToLowerInvariant();
        }
    }
}
