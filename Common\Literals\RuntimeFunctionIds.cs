﻿namespace Codeless.Server.Common
{
    public static class RuntimeFunctionIds
    {
        public const int ChangePassword = 504;
        public const int ReportOptions = 597;
        public const int ReportPreview = 681;
        public const int TasksActive = 700;
        public const int Lists = 3447;
        public const int IndexTuning = 3521;
        public const int UpdateQueueItem = 3528;
        public const int ExcelProfiler = 3561;
        public const int Proc_CIC_Update_ControlObject_Incoming = 3597;
        public const int CustomUIProfiler = 3708;
        public const int AddTask = 3747;
        public const int CustomMessage = 3748;
        public const int ExcelExportTemplate = 3439;
        public const int EditProfile = 3500;
        public const int ServicesLogs = 3503;
        public const int SecurityLogs = 3513;
        public const int ExecuteTaskSchedulerManually = 3395;
    }
}
