﻿using Codeless.Framework.IO.Standard;
using Codeless.Framework.IO.Standard.CustomExceptions;

namespace Codeless.Server.Common.FileSystem
{
    public class FileSystemFactoryExtended : FileSystemFactoryExt
    {
        public override IFileSystem GetFileSystem()
        {
            try
            {
                return new WindowsOnPremiseFileSystemExtended();
            }
            catch (Exception ex)
            {
                throw new FileSystemException("Could not initialize Codeless File System. See inner exception for details.", ex);
            }
        }
    }
}
