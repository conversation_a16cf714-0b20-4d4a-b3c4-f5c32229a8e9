﻿using System;
using System.Threading;
using System.Collections.Generic;
using Codeless.Server.Common.LifeCycle;
using Codeless.Framework.EventProcessing;
using Codeless.Framework.LifeCycleManagement;
using Codeless.Framework.EventProcessing.Event;
using Codeless.Framework.EventProcessing.Models;
using Codeless.Server.Common.EventStore.Consume;
using Codeless.Framework.EventProcessing.Event.Contracts.LifetimeEvents;
using Codeless.Framework.EventProcessing.Utils;
using Codeless.Server.Common.ConnectionInformation;
using LoggingStandard = Codeless.Framework.Logging.Standard;


#if NET
using Codeless.Framework.EventProcessing.Infrastructure;
#else
using EventStore.ClientAPI;
#endif

namespace Codeless.Server.Common.EventStore
{
    public class EventStoreManager : EventStoreManagerBase
    {
        private readonly LoggingStandard.ILogger logger;
        private readonly ILifeCycleManager<StartupStep, ShutdownStep> lifeCycleManager;
        private readonly EventProcessor eventProcessor;
        private readonly ModelContainer modelContainer;
        private Timer timerInstanceHeartbeat;
        private readonly IConfigSettings configuration;

        public EventStoreManager(IConfigSettings configuration,
                                 EventProcessor eventProcessor,
                                  ModelContainer container,
                                 ILifeCycleManager<StartupStep, ShutdownStep> lifeCycleManager,
                                 LoggingStandard.ILoggerFactory loggerFactoryStandard)
#if NET
            : base(configuration.EventStore.GetEventStoreSettings(configuration.Environment, subscribleEnabled: true, publishEnabled: true, "server", configuration.EventStore.UseMongoDBSnapshot), 
                  loggerFactoryStandard.GetLogger(typeof(EventStoreManager)))
#else
            : base(configuration.EventStore.GetEventStoreSettings(configuration.Environment, subscribleEnabled: true, publishEnabled: true, "server", configuration.EventStore.UseMongoDBSnapshot),
                  loggerFactoryStandard.GetLogger(typeof(EventStoreManager)),
                  new EventStoreConnectionSettingsBuilder())
#endif
        {
            this.configuration = configuration;
            modelContainer = container ?? throw new ArgumentNullException(nameof(container));
            this.lifeCycleManager = lifeCycleManager ?? throw new ArgumentNullException(nameof(lifeCycleManager));
            this.eventProcessor = eventProcessor ?? throw new ArgumentNullException(nameof(eventProcessor));
            this.logger = loggerFactoryStandard.GetLogger(typeof(EventStoreManager));

            lifeCycleManager.FinalizingStarted += LifeCycleManager_FinalizingStarted;
            lifeCycleManager.ShutdownStarted += LifeCycleManager_ShutdownStarted;
            modelContainer.LeaderElectedEvent += PublishLeaderElectedEvent;
            modelContainer.ManagerElectedEvent += PublishManagerElectedEvent;

            StreamsRead = GetStreamsToRead();
        }

        public void PublishLeaderElectedEvent(object sender, EventArgs e)
        {
            WriteInfoLog("PublishLeaderElectedEvent method called");

            if (e is LeaderElectedEventArgs eventArgs && ConfigSettings.Instance.PublishInstanceHeartbeat)
            {
                var instanceLeaderElected = new InstanceLeaderElected
                {
                    LeaderElectedId = eventArgs.InstanceId,
                    EventTimestamp = DateTime.UtcNow,
                    ApplicationName = ConfigSettings.Instance.ApplicationName
                };

                PublishEvent(EventStreamType.ServerHeartbeat, instanceLeaderElected);
                WriteInfoLog("Leader elected event published");
            }
        }

        public void PublishManagerElectedEvent(object sender, EventArgs e)
        {
            WriteInfoLog("PublishManagerElectedEvent method called");

            if (e is ManagerElectedEventArgs eventArgs && ConfigSettings.Instance.PublishInstanceHeartbeat)
            {
                var instanceManagerElected = new InstanceManagerElected
                {
                    ManagerElectedId = eventArgs.InstanceId,
                    EventTimestamp = DateTime.UtcNow,
                    ApplicationName = ConfigSettings.Instance.ApplicationName
                };

                PublishEvent(EventStreamType.ServerHeartbeat, instanceManagerElected);
                WriteInfoLog("Manager elected event published");
            }
        }

        protected override Guid InstanceId => modelContainer.InstanceId;

        protected override List<EventStreamType> StreamsRead { get; }

        protected override List<EventStreamType> StreamsPublished { get; } = new List<EventStreamType>()
        {
            EventStreamType.Reporting,
            EventStreamType.Profiler,
            EventStreamType.Activity,
            EventStreamType.HeartbeatInfo,
            EventStreamType.TaskActivity,
            EventStreamType.ServerHeartbeat,
            EventStreamType.DataVersions,
            EventStreamType.CacheVersions,
            EventStreamType.ActivityStatistics,
            EventStreamType.TaskActivitySchedulers,
            EventStreamType.TaskExecutionsStatistics
        };

        protected override void OnConnectionLost(OffLineReason reason)
        {
            this.logger.WriteError($"Event store connection lost: {reason}");

            if (ConfigSettings.Instance.AnyEventStoreFeatureIsEnabled)
            {
                this.lifeCycleManager.RequestShutdown();
            }
        }

        protected override void OnPublishingCompleted()
        {
            WriteInfoLog("Event store publishing complete.");
            this.lifeCycleManager.ShutdownCompleted(ShutdownStep.EventStoreStopped);
        }

#if NET
        protected override void OnReadingInitialEventsStarted(EventStreamType type, ulong firstEventId, ulong initializedEventId)
#else
        protected override void OnReadingInitialEventsStarted(EventStreamType type, long firstEventId, long initializedEventId)
#endif
        {
            StreamInfo streamInfo = this.modelContainer.StatisticsModel.GetStreamInfo(type);

            streamInfo.InitializeTimeStart = DateTime.UtcNow;
            streamInfo.FirstEventId = firstEventId;
            streamInfo.InitializedEventId = initializedEventId;
        }
#if NET
        protected override void OnEventReceived(EventStreamType type, ulong eventNumber, StreamMessageRecord eventItem)
#else
        protected override void OnEventReceived(EventStreamType type, long eventNumber, RecordedEvent eventItem)
#endif
        {
            StreamInfo streamInfo = this.modelContainer.StatisticsModel.GetStreamInfo(type);

            streamInfo.EventsProcessed++;
            streamInfo.CurrentEventId = eventNumber;

            this.eventProcessor.EventAppeared(eventItem, type);
        }

        protected override void OnReadingInitialEventsDone(EventStreamType type)
        {
            StreamInfo streamInfo = this.modelContainer.StatisticsModel.GetStreamInfo(type);
            streamInfo.InitializeTimeEnd = DateTime.UtcNow;

            switch (type)
            {
                case EventStreamType.ServerHeartbeat:
                    if (ConfigSettings.Instance.PublishInstanceHeartbeat)
                    {
                        lifeCycleManager.StartupCompleted(StartupStep.ApplicationManifestOK);
                    }
                    break;
            }
        }

        protected override void OnConnected()
        {
            timerInstanceHeartbeat = new Timer(
               new TimerCallback(SendHeartbeat),
               null,
               TimeSpan.FromSeconds(configuration.Environment.HeartbeatCheckIntervalSeconds),
               TimeSpan.FromSeconds(configuration.Environment.HeartbeatCheckIntervalSeconds));
        }

        private void LifeCycleManager_FinalizingStarted(object sender, EventArgs e)
        {
            base.Unsubscribe();
        }

        private void LifeCycleManager_ShutdownStarted(object sender, EventArgs e)
        {
            base.PublishingDone();
        }

        private void SendHeartbeat(object state)
        {
            if (ConfigSettings.Instance.PublishInstanceHeartbeat)
            {
                var instanceHeartbeatExecuted = new InstanceHeartbeatExecuted
                {
                    EventTimestamp = DateTime.UtcNow,
                    IsLeader = modelContainer.IsLeader,
                    IsManager = modelContainer.IsManager,
                    ApplicationName = ConfigSettings.Instance.ApplicationName
                };

                this.PublishEvent(EventStreamType.ServerHeartbeat, instanceHeartbeatExecuted);
            }
        }

        private List<EventStreamType> GetStreamsToRead()
        {
            var events = new List<EventStreamType>()
            {
                EventStreamType.CallCancelationRequest,
                EventStreamType.ServerHeartbeat,
            };

            if (ConfigSettings.Instance.UseEventStoreDataVersions)
            {
                events.Add(EventStreamType.DataVersions);
            }

            if (ConfigSettings.Instance.UseEventStoreCacheVersions)
            {
                events.Add(EventStreamType.CacheVersions);
            }

            if (ConfigSettings.Instance.ExpectedNumberOfBridges > 0)
            {
                events.Add(EventStreamType.BridgeHeartbeat);
            }

            if (ConfigSettings.Instance.EventStore.Enabled)
            {
                events.Add(EventStreamType.ClassState);
            }

            return events;
        }

        private void WriteInfoLog(string message)
        {
            if (logger.IsInfoEnabled)
            {
                logger.WriteInfoFormat(message);
            }
        }
    }
}