﻿namespace Codeless.Server.Common.JsonConverters
{
    public interface IRedisSerializerService
    {
        void RegisterDeserializeConverter<T>(T instance) where T : System.Text.Json.Serialization.JsonConverter;
        string Serialize<T>(T objectToSerialize, bool useNewtonsoft = false) where T : class;
        T Deserialize<T>(string data, bool useNewtonsoft = false) where T : class, new ();
    }
}
