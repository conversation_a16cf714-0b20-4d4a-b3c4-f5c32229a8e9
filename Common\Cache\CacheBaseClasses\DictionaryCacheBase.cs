﻿using System.Collections.Generic;

namespace Codeless.Server.Common.Cache.CacheBaseClasses
{
    /// <summary>
    /// Cache based on a dictionary of items are accessed by key and that are all read at once.
    /// </summary>
    public abstract class DictionaryCacheBase<TKey, TValue> : CacheBase
    {
        protected readonly Dictionary<TKey, TValue> items = new Dictionary<TKey, TValue>();

        protected virtual TValue GetCacheItem(TKey key)
        {
            EnsureCorrectDataLoaded();

            TValue item = default(TValue);
            bool inCache = false;
            this.lockManager.LockReadMode(() =>
            {
                inCache = this.items.TryGetValue(key, out item);
            });

            return item;
        }

        protected virtual bool TryGetCacheItem(TKey key, out TValue item)
        {
            EnsureCorrectDataLoaded();

            bool inCache = false;
            item = this.lockManager.LockReadMode(() =>
            {
                TValue foundItem;
                inCache = this.items.TryGetValue(key, out foundItem);
                return foundItem;
            });

            return inCache;
        }

        public override int Count()
        {
            EnsureCorrectDataLoaded();

            return base.lockManager.LockReadMode(() =>
            {
                return this.items.Count;
            });
        }

        public override void Flush()
        {
            this.lockManager.LockWriteMode(() =>
            {
                this.items.Clear();
                this.loadedVersion = null;
            });
        }
    }
}