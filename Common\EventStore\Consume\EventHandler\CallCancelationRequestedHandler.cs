﻿using Codeless.Framework.EventProcessing.Event;
using Codeless.Framework.EventProcessing.Event.Contracts.ActivityMonitor.Events;
using Codeless.Server.Common.ServiceCalls;
using System;

namespace Codeless.Server.Common.EventStore.Consume
{
    public class CallCancelationRequestedHandler : EventHandlerTypedBase<CallCancelationRequested>
    {
        public CallCancelationRequestedHandler(ModelContainer modelContainer) : base(modelContainer)
        {
        }

        public override string CreateUrlToEvent(long? eventNumber, string streamName)
        {
            string eventStoreUrlToEvent = string.Empty;

            if (eventNumber.HasValue)
            {
                eventStoreUrlToEvent = modelContainer.CreateUrlToEvent(streamName, eventNumber.Value.ToString());
            }

            return eventStoreUrlToEvent;
        }

        protected override void ProcessEvent(CallCancelationRequested eventObject, DateTime eventTime, RelatedEvent relatedEvent)
        {
            CancelationManager.Instance.Add(eventObject);
        }
    }
}
