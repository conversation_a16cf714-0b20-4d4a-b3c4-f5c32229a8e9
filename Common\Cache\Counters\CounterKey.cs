﻿using System;
using System.Linq;

namespace Codeless.Server.Common.Cache.Counters
{
    public class CounterKey
    {
        public string SequenceName { get; private set; }
        public string SequenceReference { get; private set; }
        public string Concern { get; private set; }
        public string Company { get; private set; }
        public string FullName { get; private set; }

        public CounterKey(string sequenceName, string sequenceReference, string concern, string company)
        {
            this.SequenceName = sequenceName;
            this.SequenceReference = sequenceReference;
            this.Concern = concern;
            this.Company = company;

            if (string.IsNullOrEmpty(sequenceReference))
            {
                this.FullName = $"{sequenceName}_{concern}_{company}";
            }
            else
            {
                this.FullName = $"{sequenceName}_{sequenceReference}_{concern}_{company}";
            }

            Validate();
        }

        public CounterKey(string fullname)
        {
            string[] nameParts = fullname.Split('_');
            this.FullName = fullname;
            if (nameParts.Count() < 3 || nameParts.Count() > 4)
            {
                throw new ArgumentException($"Invalid Counter object name '{fullname}'.", nameof(fullname));
            }

            if (nameParts.Count() == 4)
            {
                this.SequenceName = nameParts[0];
                this.SequenceReference = nameParts[1];
                this.Concern = nameParts[2];
                this.Company = nameParts[3];
            }
            else
            {
                this.SequenceName = nameParts[0];
                this.SequenceReference = string.Empty;
                this.Concern = nameParts[1];
                this.Company = nameParts[2];
            }

            Validate();
        }

        private void Validate()
        {
            bool concernAll = (string.Compare(this.Concern, Constants.ALL, true) == 0);
            bool companyCodeAll = (string.Compare(this.Company, Constants.ALL, true) == 0);

            if (this.Concern.Length != 2 && !concernAll)
            {
                throw new ArgumentException($"Invalid Counter object name '{this.FullName}', concern part.", nameof(FullName));
            }

            if (this.Company.Length != 2 && !companyCodeAll)
            {
                throw new ArgumentException($"Invalid Counter object name '{this.FullName}', company code part.", nameof(FullName));
            }

            if (concernAll)
            {
                this.Concern = string.Empty;
            }

            if (companyCodeAll)
            {
                this.Company = string.Empty;
            }
        }
    }
}
