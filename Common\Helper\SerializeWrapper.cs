﻿using System;
using System.IO;
using System.Runtime.Serialization;
using System.Xml.Serialization;

namespace Codeless.Server.Common
{
    public partial class XmlHelper
    {
        public class SerializeWrapper
        {
            private readonly bool useDataContracts;
            private readonly DataContractSerializer dataContractSerializer;
            private readonly XmlSerializer xmlSerializer;

            public SerializeWrapper(Type type, bool useDataContracts)
            {
                this.useDataContracts = useDataContracts;

                if (useDataContracts)
                {
                    dataContractSerializer = new DataContractSerializer(type);
                }
                else
                {
                    xmlSerializer = new XmlSerializer(type);
                }
            }


            internal string SerializeData(object dtoObject)
            {
                if (useDataContracts)
                {
                    using (MemoryStream memoryStream = new MemoryStream())
                    {
                        using (StreamReader reader = new StreamReader(memoryStream))
                        {
                            dataContractSerializer.WriteObject(memoryStream, dtoObject);
                            memoryStream.Position = 0;
                            return reader.ReadToEnd();
                        }
                    }
                }
                else
                {
                    using (StringWriter writer = new StringWriter())
                    {
                        xmlSerializer.Serialize(writer, dtoObject);

                        return writer.GetStringBuilder().ToString();
                    }
                }
            }

            internal object DeserializeData(string data)
            {
                if (useDataContracts)
                {
                    using (Stream stream = new MemoryStream())
                    {
                        byte[] dataBytes = System.Text.Encoding.UTF8.GetBytes(data);
                        stream.Write(dataBytes, 0, dataBytes.Length);
                        stream.Position = 0;
                        return dataContractSerializer.ReadObject(stream);
                    }
                }
                else
                {
                    if (ConfigSettings.Instance.RiskMitigationEnableMetadataFixForActionAfterAddUpdateWrongType && !string.IsNullOrWhiteSpace(data))
                    {
                        data = data
                            .Replace("ActionAfterAdd=\"false\"", "ActionAfterAdd=\"0\"")
                            .Replace("ActionAfterUpdate=\"false\"", "ActionAfterUpdate=\"2\"")
                            .Replace("ActionAfterAdd=\"true\"", "ActionAfterAdd=\"0\"")
                            .Replace("ActionAfterUpdate=\"true\"", "ActionAfterUpdate=\"2\"");
                    }

                    using (StringReader reader = new StringReader(data))
                    {
                        return xmlSerializer.Deserialize(reader);
                    }
                }
            }
        }
    }
}