﻿using Codeless.Framework.DependencyInjection;
using Codeless.Framework.LockManagement;
using Codeless.Framework.Logging.Standard;
using Codeless.Framework.Sql.Standard;
using Codeless.Server.Common.Cache.CacheBaseClasses;
using Codeless.Server.Common.Cache.CacheVersions;
using Codeless.Server.Common.ConnectionInformation;
using Codeless.Server.Interfaces.Common.Cache;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;

namespace Codeless.Server.Common.Cache.Company
{
    [ExcludeFromCodeCoverage]
    public class RununitsAndCompaniesCache : DictionaryCacheBase<string, List<string>>, IRununitsAndCompaniesCache
    {
        private ISqlConnectionManager sqlConnectionManager;
        private ISqlWorkerFactory sqlWorkerFactory;

        private List<string> concerns;
        private Dictionary<string, List<string>> companies;
        private Dictionary<string, string> defaultCompanies;

        public List<string> GetConcerns()
        {
            EnsureCorrectDataLoaded();
            return concerns;
        }

        public Dictionary<string, List<string>> GetCompanies()
        {
            EnsureCorrectDataLoaded();
            return companies;
        }

        public Dictionary<string, string> GetDefaultCompanies()
        {
            EnsureCorrectDataLoaded();
            return defaultCompanies;
        }

        protected override void PreLoadData()
        {
            using (var connection = sqlConnectionManager.GetNewOpenedSqlConnection(new ConnectionOptions { Concern = Constants.ALL }))
            {
                var worker = sqlWorkerFactory.GetSqlWorker(connection);

                var result = worker.ExecuteQuery<RununitCompany>(queryGetAllRununitsAndCompanies)
                                   .GroupBy(x => x.ConcernCode);

                defaultCompanies = result.ToDictionary(x => x.Key,
                                                       x => x.Select(y => y.DefaultCompanyForConcern).FirstOrDefault());

                companies = result.ToDictionary(x => x.Key,
                                                x => x.Select(y => y.CompanyCode).ToList());
            }

            concerns = companies.Keys.ToList();
        }

        class RununitCompany
        {
            public string ConcernCode { get; set; }
            public string DefaultCompanyForConcern { get; set; }
            public string CompanyCode { get; set; }
        }

        [InjectionMethod]
        public void Initialize(
            IPackageInfo packageInfo,
            ICacheVersionsCache cacheVersionsCache,
            ILockManagerFactory lockManagerFactory,
            ILoggerFactory loggerFactory,
            ISqlConnectionManager sqlConnectionManager,
            ISqlWorkerFactory sqlWorkerFactory)
        {
            this.sqlConnectionManager = sqlConnectionManager;
            this.sqlWorkerFactory = sqlWorkerFactory;

            base.Initialize(packageInfo, cacheVersionsCache, lockManagerFactory, loggerFactory, CacheVersionTypes.Company);
        }

        private readonly string queryGetAllRununitsAndCompanies = @"
            select c.[Rununit] as ConcernCode,
                   r.[DefaultCompany] as DefaultCompanyForConcern,
                   c.[Company(2)] as CompanyCode
              from [System_Company] c
              join [System_Rununit] r 
                on r.Rununit = c.Rununit";

        #region Singleton

        public static IRununitsAndCompaniesCache Instance
        {
            get
            {
                return Nested.instance;
            }
        }

        class Nested
        {
            protected Nested()
            {
            }

            static Nested()
            {
            }

            internal static readonly IRununitsAndCompaniesCache instance = new RununitsAndCompaniesCache();
        }

        #endregion
    }
}
