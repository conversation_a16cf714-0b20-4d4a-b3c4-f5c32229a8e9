﻿using Codeless.Framework.IO.Standard;
using System.Reflection;
using System.Runtime.Loader;


namespace Codeless.Server.Common.FileSystem
{
    public class WindowsOnPremiseFileSystemExtended : WindowsOnPremiseFileSystem
    {
        public WindowsOnPremiseFileSystemExtended()
        {
#if NET
            AssemblyLoadContext.Default.Resolving += CurrentDomain_AssemblyResolve;
#endif
        }

        public override Assembly LoadAssembly(string path)
        {

#if NET
            return LoadPluginAssemblyContext(path);
#else
            return base.LoadAssembly(path);
#endif
        }

        private Assembly LoadPluginAssemblyContext(string path)
        {
            CheckIfFilePathIsValid(path);

            // Lock must be used because code uses a private varaiable that is not thread safe
            Assembly assembly;
            lock (lockObject)
            {

                // Set private members (This is why this code is in a lock) 
                this.assemblyFolder = Path.GetDirectoryName(path);

                assemblyLoadFileSystem = currentFileSystem;
                if (!currentFileSystem.FileExists(path))
                {
                    string localFile = PathExt.Combine(GetCurrentAssemblyFolder(), Path.GetFileName(path));

                    if (localFileSystem.FileExists(localFile))
                    {
                        path = localFile;
                        assemblyLoadFileSystem = localFileSystem;
                    }
                }
                else
                {
                    FileInfo fileInfo = new FileInfo(path);
                    path = fileInfo.FullName;
                }

                string serverPlugin = "Codeless.Server.PlugIn.dll".ToLowerInvariant();

                if (path.ToLowerInvariant().Contains(serverPlugin))
                {
                    assembly = AssemblyLoadContext.Default.LoadFromAssemblyPath(path);

                }
                else
                {
                    PluginLoadContext pluginContext = PluginLoadContext.GetOrCreateContext(path, assemblyLoadFileSystem);
                    var assemblyBytes = this.assemblyLoadFileSystem.ReadAllBytes(path);
                    using (MemoryStream ms = new MemoryStream(assemblyBytes))
                    {
                        assembly = pluginContext.LoadFromStream(ms);
                    }

                }

            }

            return assembly;

        }
        private Assembly CurrentDomain_AssemblyResolve(AssemblyLoadContext context, AssemblyName name)
        {

            // Ensure private members are set, this ensure that the event is being fired from inside the lock and with the private memebers initialized. 
            if (assemblyFolder == null || assemblyLoadFileSystem == null)
            {
                return null;

            }

            // Ignore missing resources
            if (name.Name.Contains(".resources"))
            {
                return null;
            }

            // check for assemblies already loaded
            Assembly assembly = AppDomain.CurrentDomain.GetAssemblies().FirstOrDefault(a => a.FullName == name.FullName);
            if (assembly != null)
            {
                return assembly;
            }

            byte[] assemblyBytes = null;

            string pluginDll = PathExt.Combine(this.assemblyFolder, $"{name.Name}.dll");
            if (this.assemblyLoadFileSystem.FileExists(pluginDll))
            {
             
                assemblyBytes = this.assemblyLoadFileSystem.ReadAllBytes(pluginDll);

            }
            else
            {
                string dll = PathExt.Combine(GetCurrentAssemblyFolder(), $"{name.Name}.dll");
                if (this.localFileSystem.FileExists(dll))
                {
                    assemblyBytes = this.localFileSystem.ReadAllBytes(dll);
                }
            }
       
            if (assemblyBytes != null)
            {
                try
                {

                    using (MemoryStream ms = new MemoryStream(assemblyBytes))
                    {
                        return context.LoadFromStream(ms);
                    }
                }
                catch (Exception)
                {
                    // Ignore load exceptions, sometimes assembly can still be used even when some dependent file could not be loaded
                }
            }
            return null;


        }

    }
}
