﻿using System.Collections.Generic;

namespace Codeless.Server.Common.Cache.CacheBaseClasses
{
    /// <summary>
    /// Cache based on a list of items that are all read at once.
    /// </summary>
    public abstract class CollectionCacheBase<T> : CacheBase
    {
        protected readonly List<T> items = new List<T>();

        protected IEnumerable<T> GetCacheItems()
        {
            EnsureCorrectDataLoaded();

            return this.lockManager.LockReadMode(() =>
            {
                return this.items;
            });
        }
        public override int Count()
        {
            EnsureCorrectDataLoaded();

            return base.lockManager.LockReadMode(() =>
            {
                return this.items.Count;
            });
        }

        public override void Flush()
        {
            this.lockManager.LockWriteMode(() =>
            {
                this.items.Clear();
                this.loadedVersion = null;
            });
        }
    }
}