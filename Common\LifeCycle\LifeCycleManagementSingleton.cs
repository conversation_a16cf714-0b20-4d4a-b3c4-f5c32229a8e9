﻿using Codeless.Framework.DependencyInjection;
using Codeless.Framework.LifeCycleManagement;
using Codeless.Framework.Logging.Standard;
using Codeless.Framework.LockManagement;

namespace Codeless.Server.Common.LifeCycle
{
    public class LifeCycleManagementSingleton : ILifeCycleManagementSingleton
    {
        private ILifeCycleManager<StartupStep, ShutdownStep> lifeCycleManager;
        public ILifeCycleManager<StartupStep, ShutdownStep> GetLifeCycleManagement => lifeCycleManager;

        public bool AcceptingWork => this.lifeCycleManager.AcceptingWork;
        public bool InStartup => this.lifeCycleManager.InStartup;
        public bool IsDead => this.lifeCycleManager.IsDead;

        [InjectionMethod]
        public void Initalize(ILifeCycleSettingsProvider lifeCycleSettingsProvider, ILoggerFactory loggerFactory, ILockManagerFactory lockManagerFactory)
        {
            this.lifeCycleManager = new LifeCycleManager<StartupStep, ShutdownStep>(lifeCycleSettingsProvider, loggerFactory, lockManagerFactory);
        }

        public void StartupCompleted(StartupStep startupStep)
        {
            lifeCycleManager.StartupCompleted(startupStep);
        }

        public void ShutdownCompleted(ShutdownStep shutdownStep)
        {
            lifeCycleManager.ShutdownCompleted(shutdownStep);
        }

        public void WorkStart()
        {
            lifeCycleManager.WorkStart();
        }

        public void WorkDone()
        {
            lifeCycleManager.WorkDone();
        }

        public void RequestShutdown()
        {
            lifeCycleManager.RequestShutdown();
        }

        #region Singleton

        public static ILifeCycleManagementSingleton Instance { get => Nested.instance; }

        class Nested
        {
            protected Nested() { }
            static Nested() { }

            internal static readonly ILifeCycleManagementSingleton instance = new LifeCycleManagementSingleton();
        }

        #endregion
    }
}
