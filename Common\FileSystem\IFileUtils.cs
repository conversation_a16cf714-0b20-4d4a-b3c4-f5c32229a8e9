﻿namespace Codeless.Server.Common
{
    public interface IFileUtils
    {
        void CopyFile(string from, string to, bool overwrite);
        void CopyZipWithCheck(string zipFilePath, string destFilePath);
        void CreateDirectory(string directoryName);
        void DeleteFile(string fileName);
        bool Exists(string filePath);
        string GetFilePathWithExtension(string fullFilePath, string extensionIncludingDot = null, string newParentDirectory = null);
        bool IsFileLocked(string filePath, bool fileMustExist = false);
        void MoveDirectory(string oldDirectory, string newDirectory);
        void MoveFile(string sourceFileName, string destFileName, bool overwrite = true, bool checkIfExists = false);
        string ReadAllText(string filePath);
        void WaitLock(string filePath, bool fileMustExist = false);
        void WriteAllText(string filePath, string text);
        void WriteAllBytes(string filePath, byte[] bytes);
    }
}