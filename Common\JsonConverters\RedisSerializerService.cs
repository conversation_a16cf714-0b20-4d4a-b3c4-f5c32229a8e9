﻿using Codeless.Framework.LockManagement;
using Codeless.Framework.Utils.JsonSerialization;
using Codeless.Server.Interfaces;
using Codeless.Server.Interfaces.Services.DTO;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Text;
using System.Text.Json;
using Utils.JsonSerialization;
using Utils.JsonSerialization.Newtonsoft;

namespace Codeless.Server.Common.JsonConverters
{
    public class RedisSerializerService : IRedisSerializerService
    {
        private const string base64Prefix = "B64:";

        private readonly bool compressListsInRedis;
        private readonly int compressMinSize;

        private readonly ILockManager lockManager;

        private readonly Dictionary<Type, System.Text.Json.Serialization.JsonConverter> registeredDeserializeConverters = new Dictionary<Type, System.Text.Json.Serialization.JsonConverter>();

        private static readonly JsonSerializerOptions serializeOptions = new JsonSerializerOptions()
        {
            DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull
        };

        private JsonSerializerOptions deserializeOptions = new JsonSerializerOptions();

        public RedisSerializerService(
            ILockManagerFactory lockManagerFactory,
            IConfigSettings configSettings)
        {
            compressListsInRedis = configSettings.HttpHandlersRedisCompress;
            compressMinSize = configSettings.HttpHandlersRedisCompressMinSizeBytes;
            lockManager = lockManagerFactory.GetLockManager(typeof(RedisSerializer));
        }

        public void RegisterDeserializeConverter<T>(T instance) where T : System.Text.Json.Serialization.JsonConverter
        {
            bool registered = lockManager.LockReadMode(() =>
            {
                return registeredDeserializeConverters.ContainsKey(typeof(T));
            });


            if (!registered)
            {
                lockManager.LockWriteMode(() =>
                {
                    if (!registeredDeserializeConverters.ContainsKey(typeof(T)))
                    {
                        registeredDeserializeConverters.Add(typeof(T), instance);

                        JsonSerializerOptions newDeserializeOptions = new JsonSerializerOptions();

                        foreach (var converter in registeredDeserializeConverters.Values)
                        {
                            newDeserializeOptions.Converters.Add(converter);
                        }

                        deserializeOptions = newDeserializeOptions;
                    }
                });
            }
        }

        private static readonly JsonSerializerSettings JsonSettings = new JsonSerializerSettings()
        {
            DateTimeZoneHandling = DateTimeZoneHandling.Unspecified,
            TypeNameHandling = TypeNameHandling.All,
            Converters = new List<Newtonsoft.Json.JsonConverter>
            {
                new ListDataJsonConverter<IListData, ListData>()
            }
        };

        public string Serialize<T>(T objectToSerialize, bool useNewtonsoft = false) where T : class
        {
            string json;
            if (useNewtonsoft)
            {
                json = Encoding.UTF8.GetString(NewtonsoftSerializer.ConvertoJson(objectToSerialize, JsonSettings));
            }
            else
            {
                json = SystemTextJsonSerializer.Serialize<T>(objectToSerialize, serializeOptions);
            }

            if (compressListsInRedis && json.Length > compressMinSize)
            {
                return base64Prefix + StringCompression.Zip(json);
            }
            else
            {
                return json;
            }
        }

        public T Deserialize<T>(string data, bool useNewtonsoft = false) where T : class, new()
        {
            if (string.IsNullOrEmpty(data))
            {
                return new T();
            }

            if (data.StartsWith("<"))
            {
                return XmlHelper.Deserialize<T>(data);
            }

            if (data.StartsWith(base64Prefix))
            {
                data = data.Substring(base64Prefix.Length);
                data = StringCompression.Unzip(data);
            }

            if (useNewtonsoft)
            {
                return NewtonsoftSerializer.ConvertFromJson<T>(data, JsonSettings);
            }
            else
            {
                return SystemTextJsonSerializer.Deserialize<T>(data, deserializeOptions);
            }
        }
    }
}
