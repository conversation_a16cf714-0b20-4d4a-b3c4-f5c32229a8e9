﻿using System;
using System.Runtime.Serialization;

namespace Codeless.Server.Common.Exceptions
{
    [Serializable]
    public class LoadDataException : Exception
    {
        public LoadDataException(string message, Exception innerException)
            : base(message, innerException)
        {
        }

        public LoadDataException(string message)
          : base(message)
        {
        }

        protected LoadDataException(SerializationInfo info, StreamingContext context)
           : base(info, context)
        {
        }

        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            base.GetObjectData(info, context);
        }
    }
}
