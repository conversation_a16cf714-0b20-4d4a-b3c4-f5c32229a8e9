﻿using Codeless.Framework.Sql.Standard;
using Codeless.Framework.LockManagement;
using Codeless.Server.Common.Cache.DataVersions;
using Codeless.Server.Common.Enumerations;
using Codeless.Server.Common.Performance;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using Codeless.Framework.DependencyInjection;

namespace Codeless.Server.Common.Cache.Counters
{
    public class CountersCache : ICountersCache
    {
        private ISqlConnectionManager sqlConnectionManager;
        private ISqlWorkerFactory sqlWorkerFactory;
        private IConfigSettings configSettings;

        protected readonly int maxNumberOfInsertAttempts = ConfigSettings.Instance.SqlMaxNumberOfInsertAttempts;
        private readonly ILockManager lockManager = new LockManagerFactory(LockLogging.Instance).GetLockManager(typeof(CountersCache));

        private readonly Dictionary<string, bool> knownSequences = new Dictionary<string, bool>();
        private readonly Dictionary<string, bool> knownCounterRecords = new Dictionary<string, bool>();

        public bool SequenceExists(string name)
        {
            bool exists = false;

            lockManager.LockReadMode(() =>
            {
                exists = this.knownSequences.ContainsKey(name);
            });

            return exists;
        }

        public long GetNextValueForCounterOrSequence(IDbConnection connection, IDbTransaction transaction, string concernCode, string name)
        {
            long value;

            if (ConfigSettings.Instance.SqlUseSequences)
            {
                EnsureSequenceExists(concernCode, name, 1);
                value = GetNextSequenceValue(connection, transaction, name);
            }
            else
            {
                EnsureCounterRecordExists(connection, transaction, name);
                value = GetNextCounterRecordValue(connection, transaction, name);
            }

            return value;
        }

        public void EnsureSequenceExists(string concernCode, string name, long startingValue)
        {
            bool exists = SequenceExists(name);
            if (!exists)
            {
                lockManager.LockWriteMode(() =>
                {
                    CreateSequence(concernCode, name, startingValue);

                    if (!this.knownSequences.ContainsKey(name))
                    {
                        this.knownSequences.Add(name, true);
                    }
                });
            }
        }

        private void CreateSequence(string concernCode, string name, long startingValue)
        {
            if (string.IsNullOrWhiteSpace(name))
            {
                throw new ArgumentException("Invalid Sequence name.", "name");
            }

            if (startingValue < 0)
            {
                throw new ArgumentException("Invalid Sequence starting value (must be greater or equal to 0).", "startingValue");
            }

            using (IDbConnection connection = sqlConnectionManager.GetNewOpenedSqlConnection(concernCode, false))
            {
                ISqlWorker sqlWorker = this.sqlWorkerFactory.GetSqlWorker(connection, GetConnectionOptions());

                SqlParameter spName = new SqlParameter("@SequenceName", System.Data.SqlDbType.VarChar, 255);
                spName.Value = name;

                sqlWorker.ExecuteCommand(string.Format(sqlCreateSequence, name, startingValue), spName);
            }
        }

        private ConnectionOptions GetConnectionOptions()
        {
            return new ConnectionOptions() { CustomCommmandTimeout = configSettings.SqlCommandTimeout };
        }

        public long GetNextSequenceValue(IDbConnection connection, IDbTransaction transaction, string name)
        {
            if (string.IsNullOrWhiteSpace(name))
            {
                throw new ArgumentException("Invalid Sequence name.", "name");
            }

            ISqlWorker sqlWorker = sqlWorkerFactory.GetSqlWorker(connection, transaction, GetConnectionOptions());

            object value = sqlWorker.ExecuteScalar(string.Format(sqlGetNextSequenceValue, name, 1));

            if (value == null)
            {
                throw new ArgumentException(string.Format("Value for sequence '{0}' not found.", name));
            }

            long sequenceValue = Convert.ToInt64(value);

            if (sequenceValue == 1)
            {
                CreateCounterObject(connection, transaction, new CounterKey(name));
            }

            return sequenceValue;
        }

        public bool CounterRecordExists(string name)
        {
            bool exists = false;

            lockManager.LockReadMode(() =>
            {
                exists = this.knownCounterRecords.ContainsKey(name);
            });

            return exists;
        }

        public void EnsureCounterRecordExists(IDbConnection connection, IDbTransaction transaction, string name)
        {
            bool exists = CounterRecordExists(name);
            if (!exists)
            {
                lockManager.LockUpgradeMode(() =>
                {
                    CreateCounterObject(connection, transaction, name);

                    lockManager.LockWriteMode(() =>
                    {
                        if (!knownCounterRecords.ContainsKey(name))
                        {
                            knownCounterRecords.Add(name, true);
                        }
                    });
                });
            }
        }

        public void CreateCounterObject(IDbConnection connection, IDbTransaction transaction, string name)
        {
            CreateCounterObject(connection, transaction, new CounterKey(name));
        }

        public void CreateCounterObject(IDbConnection connection, IDbTransaction transaction, CounterKey counterKey)
        {
            int i = 0;
            bool inserted = false;
            ISqlWorker sqlWorker = sqlWorkerFactory.GetSqlWorker(connection, transaction);

            while (i < maxNumberOfInsertAttempts && !inserted)
            {
                try
                {
                    decimal objectId = RandomIdGenerator.Instance.GetRandomLong();

                    sqlWorker.ExecuteCommand(sqlCreateCounterObject,
                                new SqlParameter("ObjectId", objectId),
                                new SqlParameter("Name", counterKey.SequenceName),
                                new SqlParameter("Reference", counterKey.SequenceReference),
                                new SqlParameter("FullName", counterKey.FullName),
                                new SqlParameter("Company", counterKey.Company),
                                new SqlParameter("Concern", counterKey.Concern));

                    inserted = true;
                }
                catch (SqlException ex)
                {
                    if (ex.Number == (int)SqlErrors.CouldNotInsertDuplicateKey)
                    {
                        i++;

                        if (i == maxNumberOfInsertAttempts)
                        {
                            throw;
                        }
                    }
                    else
                    {
                        throw;
                    }
                }
            }

            DataVersionCache.Instance.IncreaseVersion("*", RuntimeClassIds.Counter);
        }

        public long GetNextCounterRecordValue(IDbConnection connection, IDbTransaction transaction, string name)
        {
            if (string.IsNullOrWhiteSpace(name))
            {
                throw new NotSupportedException("Invalid Sequence name.");
            }

            CounterKey counterKey = new CounterKey(name);

            ISqlWorker sqlWorker = sqlWorkerFactory.GetSqlWorker(connection, transaction, GetConnectionOptions());
            object value = sqlWorker.ExecuteScalar(sqlGetNextCounterRecordValue,
                                                   new SqlParameter("Name", counterKey.SequenceName),
                                                   new SqlParameter("Reference", counterKey.SequenceReference),
                                                   new SqlParameter("Company", counterKey.Company),
                                                   new SqlParameter("Concern", counterKey.Concern));

            if (value == null)
            {
                throw new NotSupportedException($"Value for sequence '{name}' not found.");
            }

            if (!long.TryParse(value.ToString(), out long result))
            {
                throw new NotSupportedException($"Value for sequence '{name}' could not be converted to long.");
            }

            return result;
        }

        private readonly string sqlCreateSequence =
            @"
            IF NOT EXISTS (SELECT seq.name AS [Sequence name] FROM sys.sequences AS seq 
                JOIN sys.schemas AS sch ON seq.schema_id = sch.schema_id WHERE seq.name = @SequenceName)
	            CREATE SEQUENCE [{0}] START WITH {1} INCREMENT BY 1 CACHE
            ";

        private const string sqlGetNextSequenceValue =
            @"
            IF NOT EXISTS (SELECT seq.name AS [Sequence name] FROM sys.sequences AS seq 
                JOIN sys.schemas AS sch ON seq.schema_id = sch.schema_id WHERE seq.name = '{0}')
	            CREATE SEQUENCE [{0}] START WITH {1} INCREMENT BY 1 CACHE
    
            SELECT NEXT VALUE FOR [{0}]
            ";

        private const string sqlGetNextCounterRecordValue =
            @"
            UPDATE [CP0000005309_ALL]
            SET
	            [AP0000087442] = [AP0000087442] + 1
            OUTPUT Inserted.[AP0000087442]
            WHERE
	            [AP0000087440]=@Company AND
	            [AP0000087434]=@Name AND
	            [AP0000087438]=@Reference AND
	            [AP0000087439]=@Concern
            ";

        private const string sqlCreateCounterObject =
            @"
            IF NOT EXISTS
	            (
	            SELECT
		            [OBJECT_ID]
	            FROM
		            [CP0000005309_ALL]
	            WHERE
		            [AP0000087440]=@Company AND
		            [AP0000087434]=@Name AND
		            [AP0000087438]=@Reference AND
		            [AP0000087439]=@Concern
	            )
            BEGIN

            INSERT INTO [CP0000005309_ALL]
	            ([OBJECT_ID]
	            ,[COMPANY]
	            ,[STATE]
	            ,[ADD_UID]
	            ,[ADD_TIME]
	            ,[MODIFY_UID]
	            ,[MODIFY_TIME]
	            ,[VERSION]
	            ,[AP0000087441]
	            ,[AP0000087440]
	            ,[AP0000087434]
	            ,[AP0000087438]
	            ,[AP0000087439]
	            ,[AP0000087442]
	            ,[FK_5237]
	            ,[FK_5236])
            VALUES
	            (@ObjectId
	            ,'*'
	            ,1
	            ,'Server'
	            ,GETDATE()
	            ,'Server'
	            ,GETDATE()
	            ,1
	            ,@FullName
	            ,@Company
	            ,@Name
	            ,@Reference
	            ,@Concern
	            ,1
	            ,0
	            ,0)

            UPDATE [CP0000005309_ALL]
            SET
	            [FK_5236] = (SELECT ISNULL((SELECT TOP 1 [OBJECT_ID] FROM [CP0000001107_ALL] WHERE [AP0000013908] = @Concern), 0))
	            ,[FK_5237] = (SELECT ISNULL((SELECT TOP 1 [OBJECT_ID] FROM [CP0000001108_ALL] WHERE [AP0000013911] = @Company), 0))
            WHERE
	            [OBJECT_ID] = @ObjectId

            END
            ";

        [InjectionMethod]
        public void Initialize(ISqlConnectionManager ConnectionManager, ISqlWorkerFactory sqlWorkerFactory, IConfigSettings configSettings)
        {
            this.sqlConnectionManager = ConnectionManager ?? throw new ArgumentNullException(nameof(ConnectionManager));
            this.sqlWorkerFactory = sqlWorkerFactory ?? throw new ArgumentNullException(nameof(sqlWorkerFactory));
            this.configSettings = configSettings ?? throw new ArgumentNullException(nameof(configSettings));
        }

        #region Singleton

        public static ICountersCache Instance
        {
            get
            {
                return Nested.instance;
            }
        }

        class Nested
        {
            protected Nested()
            { }
            static Nested()
            {
            }

            internal static readonly ICountersCache instance = new CountersCache();
        }

        #endregion

    }
}