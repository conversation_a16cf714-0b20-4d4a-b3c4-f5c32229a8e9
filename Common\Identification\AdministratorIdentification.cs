﻿using System;
using Id = Codeless.Server.CommunicationClasses.Identification;

namespace Codeless.Server.Common.Identification
{
    public static class AdministratorIdentification
    {
        public static Id GetAdministrator()
        {
            return new Id
            {
                Username = ConfigSettings.Instance.AdminUserName,
                Password = ConfigSettings.Instance.AdminHashedPassword,
                ConcernName = "*",
                CompanyName = "*",
                MachineName = "<Server>",
                SessionId = Guid.NewGuid()
            };
        }

        public static Id GetAdministrator(string concern, string company)
        {
            return new Id
            {
                Username = ConfigSettings.Instance.AdminUserName,
                Password = ConfigSettings.Instance.AdminHashedPassword,
                ConcernName = concern,
                CompanyName = company,
                MachineName = "<Server>",
                SessionId = Guid.NewGuid()
            };
        }
    }
}
