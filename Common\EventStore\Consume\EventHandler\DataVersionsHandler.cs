﻿using Codeless.Framework.EventProcessing.Event;
using Codeless.Framework.EventProcessing.Event.Contracts.Versions;
using Codeless.Framework.EventProcessing.EventHandling;
using Codeless.Server.Common.Cache.DataVersions;
using System;

namespace Codeless.Server.Common.EventStore.Consume
{
    public class DataVersionsHandler : HandlerBase<DataVersions>
    {
        public DataVersionsHandler(ModelContainer modelContainer) : base(modelContainer)
        {
        }

        protected override void ProcessEvent(DataVersions eventObject, DateTime eventTime, RelatedEvent relatedEvent)
        {
            DataVersionCache.Instance.IncreaseVersion(eventObject.ConcernCode, eventObject.ClassId, new DataVersion(eventObject.DataVersion, eventObject.TimeStamp));
        }
    }
}
