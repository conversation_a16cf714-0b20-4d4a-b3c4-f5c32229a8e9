﻿using Codeless.Framework.DependencyInjection;
using Codeless.Framework.EventProcessing;
using Codeless.Framework.Logging.Standard;
using Codeless.Framework.LockManagement;
using Codeless.Server.Common.Cache.CacheBaseClasses;
using Codeless.Server.Common.ProducerConsumerPattern;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Codeless.Server.Common.EventStore
{
    public class EventsGenerator : BackgroundWorkerBase, IEventsGenerator
    {
        private const int MaximumNumberOfGeneratedEvents = 1000000;
        private const int MillisecondsInAnHour = 3600000;

        // Dependencies
        private ILockManager lockManager;
        private IProducerConsumer producerConsumer;

        // Statistics
        private readonly EventsGenerationGlobalInfo eventsGenerationGlobalInfo = new EventsGenerationGlobalInfo();
        private ObjectCache<Guid, EventsGenerationInfo> requestHistory;

        private EventsGeneratorWorker eventsGeneratorWorker;

        protected override string Name => nameof(EventsGenerator);

        protected override void DoWork()
        {
            // Workers threads will be started on first request.
        }

        protected override List<Task> StopWork()
        {
            return this.producerConsumer.StopChannel<EventsGenerationRequest>();
        }

        public EventsGenerationGlobalInfo GetGlobalInfo()
        {
            return lockManager.LockReadMode(() =>
            {
                EventsGenerationGlobalInfo result = this.eventsGenerationGlobalInfo.Clone();
                result.Requests = requestHistory.GetAll().Values.ToList();
                result.Workers = this.producerConsumer.GetWorkerCount<EventsGenerationRequest>();

                result.PendingRequests = result.Requests.Count((x) => x.Status != EventsGenerationRequestStatus.Completed && 
                                                                      x.Status != EventsGenerationRequestStatus.Failed);

                return result;
            });
        }

        public void RequestEventsGeneration(EventsGenerationEventType eventsType, bool failed, int count, int intervalMin, int intervalMax)
        {
            ValidateGenerationInput(ref count, ref intervalMin, ref intervalMax);

            eventsGenerationGlobalInfo.LastRequestAdded = DateTime.Now;

            EventsGenerationRequest request = new EventsGenerationRequest() 
            { 
                EventsType = eventsType, 
                Failed = failed, 
                Count = count, 
                IntervalMin = intervalMin, 
                IntervalMax = intervalMax 
            };

            EventsGenerationInfo eventsGenerationInfo = new EventsGenerationInfo(request, EventsGenerationRequestStatus.Queued);
            requestHistory.TryAdd(eventsGenerationInfo.Id, eventsGenerationInfo, 1);

            this.producerConsumer.AddRequest(request);
        }

        private void OnEventsGeneration(EventsGenerationRequest request)
        {
            Task.Factory.StartNew(() =>
            {
                try
                {
                    EventsGenerationInfo eventsGenerationInfo;
                    if (requestHistory.TryGet(request.Id, out eventsGenerationInfo))
                    {
                        eventsGenerationInfo.StartGeneration = DateTime.Now;
                    }
                    else
                    {
                        eventsGenerationInfo = new EventsGenerationInfo(request, EventsGenerationRequestStatus.Queued);
                    }

                    lockManager.LockWriteMode(() =>
                    {
                        this.eventsGenerationGlobalInfo.LastRequestStarted = DateTime.Now;
                    });

                    eventsGeneratorWorker.GenerateEvents(eventsGenerationInfo);

                    eventsGenerationInfo.EndGeneration = DateTime.Now;
                }
                catch (Exception ex)
                {
                    this.logger.WriteError($"Failed to generate event(s): {ex.Message}", ex);
                }
                finally
                {
                    lockManager.LockWriteMode(() =>
                    {
                        this.eventsGenerationGlobalInfo.LastRequestFinished = DateTime.Now;
                    });
                }
            });
        }

        private void OnEventsGenerationError(Exception ex, EventsGenerationRequest _)
        {
            this.logger.WriteError($"Failed to generate event(s): {ex.Message}", ex);
        }

        private void ValidateGenerationInput(ref int count, ref int minValue, ref int maxValue)
        {
            if (minValue < 0)
            {
                this.logger.WriteWarning($"An invalid min time interval was requested, '{minValue}', defaulting to '10'.");
                minValue = 10;
            }

            if (maxValue > MillisecondsInAnHour)
            {
                this.logger.WriteWarning($"An invalid min time interval was requested, '{maxValue}', defaulting to '100'.");
                maxValue = 100;
            }

            if (minValue > maxValue)
            {
                (maxValue, minValue) = (minValue, maxValue);
            }

            if (count <= 0 || count > MaximumNumberOfGeneratedEvents)
            {
                this.logger.WriteWarning($"An invalid number of events {count} was requested for generation, defaulting to '1'.");
                count = 1;
            }
        }

        [InjectionMethod]
        public void Initialize(
            ILoggerFactory loggerFactory,
            IConfigSettings configSettings,
            ILockManagerFactory lockManagerFactory,
            IEventStoreManager eventStoreManager,
            IProducerConsumer producerConsumer)
        {
            _ = loggerFactory ?? throw new ArgumentNullException(nameof(loggerFactory));
            _ = configSettings ?? throw new ArgumentNullException(nameof(configSettings));
            _ = lockManagerFactory ?? throw new ArgumentNullException(nameof(lockManagerFactory));
            _ = eventStoreManager ?? throw new ArgumentNullException(nameof(eventStoreManager));
            this.producerConsumer = producerConsumer ?? throw new ArgumentNullException(nameof(producerConsumer));

            this.logger = loggerFactory.GetLogger(typeof(EventsGenerator));
            this.lockManager = lockManagerFactory.GetLockManager(typeof(EventsGenerator));

            this.eventsGeneratorWorker = new EventsGeneratorWorker(loggerFactory, eventStoreManager);

            this.requestHistory = new ObjectCache<Guid, EventsGenerationInfo>(100, loggerFactory, lockManagerFactory);

            eventsGenerationGlobalInfo.MaxWorkers = configSettings.EventStoreEventsGenerationWorkers;

            this.producerConsumer.RegisterChannel<EventsGenerationRequest>(
                "Events Generator",
                OnEventsGeneration,
                OnEventsGenerationError,
                0,
                configSettings.EventStoreEventsGenerationWorkers);

        }

        #region Singleton

        private EventsGenerator() { }

        public static IEventsGenerator Instance
        {
            get
            {
                return Nested.instance;
            }
        }

        class Nested
        {
            protected Nested()
            { }
            static Nested()
            {
            }

            internal static readonly IEventsGenerator instance = new EventsGenerator();
        }

        #endregion
    }
}