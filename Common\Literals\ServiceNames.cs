﻿namespace Codeless.Server.Common.Literals
{
    public static class ServiceNames
    {
        public const string AddObjects = "AddObjects";
        public const string DeleteObjects = "DeleteObjects";
        public const string ModifyObjects = "ModifyObjects";
        public const string NavigateObjects = "NavigateObjects";
        public const string ProcessObjects = "ProcessObjects";
        public const string WrapperCall = "WrapperCall";
        public const string GetNewObject = "GetNewObject";
        public const string GetNewObjectSubCall = "GetNewObject_SubCall";
        public const string GetObject = "GetObject";
        public const string GetObjectSubCall = "GetObject_SubCall";
        public const string ModifyObject = "ModifyObject";
        public const string ModifyObjectSubCall = "ModifyObject_SubCall";
        public const string DeleteObject = "DeleteObject";
        public const string DeleteObjectSubCall = "DeleteObject_SubCall";
        public const string ProcessObject = "ProcessObject";
        public const string ProcessObjectSubCall = "ProcessObject_SubCall";
        public const string AddInitializedObject = "AddInitializedObject";
        public const string AddInitializedObjectSubCall = "AddInitializedObject_SubCall";
        public const string GetConstraintSQL = "GetConstraintSQL";
        public const string GetFilesList = "GetFilesList";
        public const string List = "List";
        public const string ListAsync = "ListAsync";
        public const string ExcelExportAsync = "ExcelExportAsync";
        public const string XmlTypeHandlerServerWriteProvider = "XmlTypeHandler.ServerWriteProvider";
        public const string XmlTypeHandlerServerWriteProviderWriteData = "XmlTypeHandler.ServerWriteProvider.WriteData";
        public const string XmlTypeHandlerServerDeleteProvider = "XmlTypeHandler.ServerDeleteProvider";
        public const string ExcelImport = "ExcelImport";
        public const string ValidateObject = "ValidateObject";
        public const string AutoValidateObject = "AutoValidateObject";
    }
}
