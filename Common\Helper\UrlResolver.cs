﻿using System;
using System.Diagnostics;
using System.Text.RegularExpressions;

namespace Codeless.Server.Common.Helper
{
    public class UrlResolver
    {
        private readonly IConfigSettings configSettings;

        public UrlResolver(IConfigSettings configSettings)
        {
            this.configSettings = configSettings ?? throw new ArgumentNullException(nameof(configSettings));
        }

        public string ParseUrl(string url, string roolUrl = null)
        {
            if (Debugger.IsAttached)
            {
                // Regex pattern to capture the hostname
                string pattern = @"^(https?://)([^:/]+)(:\d+)?";

                Match match = Regex.Match(url, pattern, RegexOptions.None, TimeSpan.FromMilliseconds(configSettings.RegExTimeoutMs));

                if (match.Success)
                {
                    string newUrl = roolUrl ?? configSettings.RootUrl;

                    // Check if there are query strings
                    int queryIndex = url.IndexOf('?');
                    if (queryIndex >= 0)
                    {
                        newUrl += url.Substring(url.IndexOf('/', match.Index + match.Length), queryIndex - (match.Index + match.Length));
                        newUrl += url.Substring(queryIndex);
                    }
                    else
                    {
                        newUrl += url.Substring(url.IndexOf('/', match.Index + match.Length));
                    }

                    return newUrl;
                }
            }

            // Return the original URL if no match is found or if the debugger is not attached
            return url;
        }
    }
}