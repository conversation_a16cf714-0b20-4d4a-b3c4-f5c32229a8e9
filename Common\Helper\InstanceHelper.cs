﻿using Codeless.Framework.EventProcessing.Models;
using Codeless.Server.Common.EventStore;
using Codeless.Server.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.NetworkInformation;
using System.Net.Sockets;

namespace Codeless.Server.Common.Helper
{
    public static class InstanceHelper
    {
        private static IModelContainer modelContainer = EventStoreManagerSingleton.Instance.GetModelContainer();
        public static Guid CurrentInstanceId => modelContainer.InstanceId;
        public static string CurrentMachineName => Dns.GetHostName();

        public static InstanceModelInfo GetInstanceDetails(Guid instanceId)
        {
            InstanceModelInfo instanceModel = new InstanceModelInfo();
            InstanceModel instanceDetails = modelContainer.Instances
                .Where(i => i.Value.InstanceStatus == Status.Running && i.Value.InstanceId == instanceId)
                .Select(i => i.Value)
                .FirstOrDefault();

            // Check if the current instance matches the instanceId
            if (instanceDetails == null && instanceId == modelContainer.InstanceId)
            {
                instanceDetails = new InstanceModel
                {
                    InstanceId = CurrentInstanceId,
                    ApplicationName = ConfigSettings.Instance.ApplicationName,
                    MachineName = CurrentMachineName,
                    IP = LocalIpAddress().ToString(),
                    StartTime = DateTime.UtcNow,
                    StoppedTime = null,
                    LastActivity = DateTime.UtcNow,
                    InstanceStatus = Status.Running,
                    IsLeader = modelContainer.IsLeader,
                    IsManager = modelContainer.IsManager
                };
            }

            if (instanceDetails != null)
            {
                instanceModel = new InstanceModelInfo
                {
                    InstanceId = instanceDetails.InstanceId,
                    MachineName = instanceDetails.MachineName,
                    IP = instanceDetails.IP,
                    RelayUrl = GetInstanceRequestUrl(ConfigSettings.Instance.IsDeployedOnPremise ? instanceDetails.RelayRequestCnameUrl : instanceDetails.IP),
                    StartTime = instanceDetails.StartTime,
                    IsLeader = instanceDetails.IsLeader,
                    IsManager = instanceDetails.IsManager
                };
            }

            return instanceModel;
        }
        public static List<InstanceModelInfo> GetRunningInstances()
        {
            var instances = modelContainer.Instances
                    .Where(i => i.Value.InstanceStatus == Status.Running && i.Value.InstanceId != CurrentInstanceId)
                    .Select(p => p.Value)
                    .GroupBy(i => i.InstanceId)
                    .Select(g => g.First())
                    .Select(i => new InstanceModelInfo
                    {
                        InstanceId = i.InstanceId,
                        IP = i.IP,
                        RelayUrl = GetInstanceRequestUrl(ConfigSettings.Instance.IsDeployedOnPremise ? i.RelayRequestCnameUrl : i.IP),
                        IsLeader = i.IsLeader,
                        MachineName = i.MachineName,
                        StartTime = i.StartTime,
                        IsManager = i.IsManager
                    }).ToList();

            //add the current instance at the top
            var currentInstance = GetInstanceDetails(CurrentInstanceId);
            instances.RemoveAll(x => x.InstanceId == currentInstance.InstanceId);
            instances.Insert(0, currentInstance);

            return instances;
        }

        public static string GetInstanceRequestUrl(string relayUrlOrIp, string endpointUrl = "")
        {
            var isOnPremise = ConfigSettings.Instance.IsDeployedOnPremise;

            string relayRequestBaseUrl = ConfigSettings.Instance.RelayRequestBaseUrl;

            if (isOnPremise)
            {
                if (string.IsNullOrWhiteSpace(relayUrlOrIp))
                {
                    throw new InvalidOperationException("Relay URL is required for on-premise deployment.");
                }

                relayRequestBaseUrl = $"{relayUrlOrIp}/Server";
            }
            else
            {
                relayRequestBaseUrl = relayRequestBaseUrl.TrimEnd("/".ToCharArray()).Replace("[InstanceName]", relayUrlOrIp).ToLowerInvariant();
            }

            if (string.IsNullOrWhiteSpace(endpointUrl))
                return relayRequestBaseUrl;

            var absolutePath = $"/{new Uri(relayRequestBaseUrl).AbsolutePath.Trim('/')}/";

            endpointUrl = NormalizeUrl(endpointUrl);

            if (endpointUrl.StartsWith(absolutePath))
            {
                endpointUrl = endpointUrl.Replace(absolutePath, string.Empty);
            }

            return $"{relayRequestBaseUrl}/{endpointUrl.Trim('/')}";
        }

        public static string GetApplicationInstanceUrl(string applicationName, string endpointUrl = "")
        {
            var instances = modelContainer.Instances
            .Where(i => string.Equals(i.Value.ApplicationName, applicationName, StringComparison.OrdinalIgnoreCase)
                        && i.Value.InstanceStatus == Status.Running)
                    .Select(i => i.Value)
                    .GroupBy(i => i.InstanceId)
                    .Select(g => g.OrderByDescending(i => i.LastEventNumber).First())
                    .FirstOrDefault() ?? throw new InvalidOperationException($"No running instance found for application {applicationName}");
            
            var applicationUrl = ConfigSettings.Instance.IsDeployedOnPremise ? instances.RelayRequestCnameUrl : instances.IP;
            
            return GetInstanceRequestUrl(applicationUrl, endpointUrl);
        }

        private static string NormalizeUrl(string endpointUrl)
        {
            string path = ExtractPath(endpointUrl);
            string queryParams = ExtractQueryParams(endpointUrl);

            string lowercasedPath = path.Trim('/').ToLowerInvariant();
            endpointUrl = $"/{lowercasedPath}{queryParams}";
            return endpointUrl;
        }

        private static string ExtractPath(string url)
        {
            int questionMarkIndex = url.IndexOf('?');
            return questionMarkIndex >= 0 ? url.Substring(0, questionMarkIndex) : url;
        }

        private static string ExtractQueryParams(string url)
        {
            int questionMarkIndex = url.IndexOf('?');
            return questionMarkIndex >= 0 ? url.Substring(questionMarkIndex) : "";
        }

        private static IPAddress LocalIpAddress()
        {
            if (!NetworkInterface.GetIsNetworkAvailable())
            {
                throw new WebException("No network is available.");
            }

            IPHostEntry host = Dns.GetHostEntry(Dns.GetHostName());
            return host.AddressList.FirstOrDefault(ip => ip.AddressFamily == AddressFamily.InterNetwork);
        }
    }
}