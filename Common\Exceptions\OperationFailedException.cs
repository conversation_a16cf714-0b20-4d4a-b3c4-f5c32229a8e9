﻿using System;
using System.Runtime.Serialization;

namespace Codeless.Server.Common.Exceptions
{
    [Serializable]
    public class OperationFailedException : Exception
    {
        public OperationFailedException(string message)
            : base(message)
        {
        }

        public OperationFailedException(string message, Exception innerException)
           : base(message, innerException)
        {
        }

        protected OperationFailedException(SerializationInfo info, StreamingContext context)
          : base(info, context)
        {
        }

        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            base.GetObjectData(info, context);
        }
    }
}
