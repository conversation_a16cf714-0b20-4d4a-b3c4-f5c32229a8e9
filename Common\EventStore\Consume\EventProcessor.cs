﻿using AutoMapper;
using Codeless.Framework.EventProcessing.Configuration;
using Codeless.Framework.EventProcessing.Event;
using Codeless.Framework.EventProcessing.Event.Contracts.ActivityMonitor.Events;
using Codeless.Framework.EventProcessing.Event.Contracts.Bridge.Events;
using Codeless.Framework.EventProcessing.Event.Contracts.ClassState.Events;
using Codeless.Framework.EventProcessing.Event.Contracts.Versions;
using Codeless.Framework.EventProcessing.MongoDB;
using Codeless.Server.Common.ConnectionInformation;
using Codeless.Server.Common.EventStore.Consume.EventHandler;
using Codeless.Server.Common.Helper;

namespace Codeless.Server.Common.EventStore.Consume
{
    public class EventProcessor : EventProcessorBase
    {
        private readonly ModelContainer model;

        public EventProcessor(EventStoreBase configuration, IMapper mapper, Framework.Logging.Standard.ILoggerFactory loggerFactory, ModelContainer modelContainer, ISnapshotStatisticsService snapshotStatisticsService)
            : base(configuration, mapper, loggerFactory.GetLogger(typeof(EventProcessor)), modelContainer, modelContainer.StatisticsModel, snapshotStatisticsService)
        {
            model = modelContainer;
            RegisterHandlers();
        }

        public override void RegisterHandlers()
        {
            RegisterHandler<CallCancelationRequested>(new CallCancelationRequestedHandler(model));

            if (ConfigSettings.Instance.EventStore.Enabled && ConfigSettings.Instance.UseEventStoreDataVersions)
            {
                RegisterHandler<DataVersions>(new DataVersionsHandler(model));
            }

            if (ConfigSettings.Instance.EventStore.Enabled && ConfigSettings.Instance.UseEventStoreCacheVersions)
            {
                RegisterHandler<CacheVersionIncreased>(new CacheVersionsHandler(model));
                RegisterHandler<PersonalCacheVersionIncreased>(new PersonalCacheVersionsHandler(model));
            }

            if (ConfigSettings.Instance.ExpectedNumberOfBridges > 0)
            {
                RegisterHandler<BridgeInstanceHeartbeatExecuted>(new BridgeInstanceHeartbeatHandler(model));
            }

            if (ConfigSettings.Instance.EventStore.Enabled)
            {
                RegisterHandler<UpdateStarted>(new ClassUpdateStartedEventHandler(model));
                RegisterHandler<UpdateCompleted>(new ClassUpdateCompletedEventHandler(model));
                RegisterHandler<UpdateFailed>(new ClassUpdateFailedEventHandler(model));
                RegisterHandler<ManuallySetAvailable>(new ClassManuallySetAvailableEventHandler(model));
            }
        }
    }
}
