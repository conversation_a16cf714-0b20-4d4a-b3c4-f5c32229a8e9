﻿namespace Codeless.Server.Common.Cache.CustomMessages
{
    public class CustomMessageKey
    {
        public CustomMessageKey() { }

        public CustomMessageKey(int messageNumber, int? languageId = null)
        {
            MessageNumber = messageNumber;
            LanguageId = languageId;
        }

        public int MessageNumber { get; set; }
        public int? LanguageId { get; set; }
        public int? ClassId { get; set; }
        public int? MessageId { get; set; }
        public int? FunctionId { get; set; }
        public int? InvariantId { get; set; }
        public int? StateId { get; set; }
        public string CallName { get; set; }
        public int? AssociationId { get; set; }
        public int? NavigationId { get; set; }
        public int? AttributeId { get; set; }
        public int? TransitionId { get; set; }

        public string ParameterList
        {
            get
            {
                return $"~{MessageNumber}~{LanguageId}~{ClassId}~{StateId}~{FunctionId}~{AssociationId}~{InvariantId}~{NavigationId}~{MessageId}~{CallName}~{AttributeId}~{TransitionId}~";
            }
        }
    }
}
