﻿using Codeless.Framework.EventProcessing;
using Codeless.Framework.EventProcessing.Event;
using Codeless.Server.Common.EventStore;
using System.Collections.Concurrent;
using System.Collections.Generic;

namespace Codeless.Server.Common.Cache.DataVersions
{
    public class DataVersionEventBased : DataVersionCache
    {
        private IEventStoreManager eventStoreManager;
        protected ConcurrentDictionary<(string concern, int classId), DataVersion> localCacheEventBased;

        protected override DataVersion GetVersionFromCache(string concern, int classId, bool assignConcern = false)
        {
            if (localCacheEventBased.TryGetValue((concern, classId), out DataVersion dataVersion) && assignConcern)
            {
                dataVersion.Concern = concern;
            }

            return dataVersion;
        }

        protected override void Initialize()
        {
            eventStoreManager = EventStoreManagerSingleton.Instance.GetEventStoreManager();
            localCacheEventBased = new ConcurrentDictionary<(string concern, int classId), DataVersion>();
        }

        protected override DataVersion SetVersionToCache(string concern, int classId, DataVersion version, bool publishEvent = false)
        {
            UpdateLocalCache(concern, classId, version);

            if (publishEvent)
            {
                PublishEvent(concern, classId, version);
            }

            return version;
        }

        private void PublishEvent(string concern, int classId, DataVersion dataVersion)
        {
            var dataVersionEventPayload = new Codeless.Framework.EventProcessing.Event.Contracts.Versions.DataVersions() { ClassId = classId, ConcernCode = concern, DataVersion = dataVersion.Version, TimeStamp = dataVersion.TimeStamp };

            eventStoreManager.PublishEvent(EventStreamType.DataVersions, dataVersionEventPayload);
        }

        private void UpdateLocalCache(string concern, int classId, DataVersion dataVersion)
        {
            if (localCacheEventBased.TryGetValue((concern, classId), out DataVersion currentDataVersion))
            {
                Update(concern, classId, dataVersion, currentDataVersion);
            }
            else
            {
                if (!localCacheEventBased.TryAdd((concern, classId), dataVersion) && localCacheEventBased.TryGetValue((concern, classId), out currentDataVersion))
                {
                    Update(concern, classId, dataVersion, currentDataVersion);
                }
            }
        }

        private void Update(string concern, int classId, DataVersion dataVersion, DataVersion currentDataVersion)
        {
            if (currentDataVersion.Version < dataVersion.Version || dataVersion.Version == 1)
            {
                localCacheEventBased.TryUpdate((concern, classId), dataVersion, currentDataVersion);
            }
        }
    }
}
