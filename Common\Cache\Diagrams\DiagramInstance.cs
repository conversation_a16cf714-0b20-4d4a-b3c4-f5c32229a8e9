﻿using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;
using System.Xml.Serialization;

namespace Codeless.Server.Common.Cache.Diagrams
{
    [Serializable]
    public class DiagramInstance
    {
        public string CompanyCode { get; set; }
        public string Rununit { get; set; }
        public decimal RootObjectId { get; set; }
        public string Data { get; set; }

        [Newtonsoft.Json.JsonIgnore]
        [JsonIgnore]
        [XmlIgnore]
        public string FilteredData { get; set; }

        public decimal DiagramId { get; set; }

        [Newtonsoft.Json.JsonIgnore]
        [JsonIgnore]
        [XmlIgnore]
        public Diagram DiagramMapping { get; set; }

        [Newtonsoft.Json.JsonIgnore]
        [JsonIgnore]
        [XmlIgnore]
        public Dictionary<string, List<Dictionary<string, string>>> ParsedData { get; set; }

        [Newtonsoft.Json.JsonIgnore]
        [JsonIgnore]
        [XmlIgnore]
        public Dictionary<string, List<Dictionary<string, string>>> FilteredParsedData { get; set; }

        public void ParseData()
        {
            ParsedData = Newtonsoft.Json.JsonConvert.DeserializeObject<Dictionary<string, List<Dictionary<string, string>>>>(Data);
        }

        public void FilterData()
        {
            FilteredData = Newtonsoft.Json.JsonConvert.SerializeObject(FilteredParsedData);
        }
    }
}
