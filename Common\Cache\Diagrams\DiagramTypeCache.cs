﻿using Codeless.Framework.DependencyInjection;
using Codeless.Framework.LockManagement;
using Codeless.Framework.Logging.Standard;
using Codeless.Framework.Sql.Standard;
using Codeless.Server.Common.Cache.CacheBaseClasses;
using Codeless.Server.Common.Cache.CacheVersions;
using Codeless.Server.Common.ConnectionInformation;
using Codeless.Server.Common.Exceptions;
using Codeless.Server.Interfaces.Common.Cache;
using System;
using System.Data;
using System.Data.SqlClient;
using System.Linq;

namespace Codeless.Server.Common.Cache.Diagrams
{
    public class DiagramTypeCache : DictionaryJITLoadCacheBase<string, DiagramType>, IDiagramTypeCache
    {
        private ISqlWorkerFactory sqlWorkerFactory;
        private ISqlConnectionManager sqlConnectionManager;

        [InjectionMethod]
        public void Initialize(
            IPackageInfo packageInfo,
            ICacheVersionsCache cacheVersionsCache,
            ILockManagerFactory lockManagerFactory,
            ILoggerFactory loggerFactory,
            ISqlWorkerFactory sqlWorkerFactory,
            ISqlConnectionManager sqlConnectionManager)
        {
            Initialize(
                packageInfo,
                cacheVersionsCache,
                lockManagerFactory,
                loggerFactory,
                CacheVersionTypes.DiagramType);

            this.sqlWorkerFactory = sqlWorkerFactory ?? throw new ArgumentNullException(nameof(sqlWorkerFactory));
            this.sqlConnectionManager = sqlConnectionManager ?? throw new ArgumentNullException(nameof(sqlConnectionManager));
        }

        public DiagramType GetDiagramType(string name)
        {
            return GetCacheItem(name);
        }

        protected override DiagramType LoadDataItem(string key)
        {
            using (IDbConnection connection = sqlConnectionManager.GetNewOpenedSqlConnection(Constants.ALL, false))
            {
                ISqlWorker sqlWorker = sqlWorkerFactory.GetSqlWorker(connection);
                DiagramType result;

                try
                {
                    result = sqlWorker.ExecuteQuery<DiagramType>(queryGetDiagramType,
                        new SqlParameter("@Name", key)).First();

                    result.DiagramElements = sqlWorker.ExecuteQuery<DiagramElement>(queryGetDiagramElements,
                        new SqlParameter("@DiagramType", result.Name)).ToList();

                    foreach (DiagramElement element in result.DiagramElements)
                    {
                        element.ElementProperties = sqlWorker.ExecuteQuery<DiagramElementProperty>(queryGetDiagramProperties,
                            new SqlParameter("@DiagramType", result.Name),
                            new SqlParameter("@DiagramElement", element.Name)).ToList();

                        if (element.IsConnector)
                        {
                            element.ConnectorReferences = sqlWorker.ExecuteQuery<DiagramElementConnectorReference>(queryGetConnectorReferences,
                                new SqlParameter("@DiagramType", result.Name),
                                new SqlParameter("@DiagramElement", element.Name)).ToList();
                        }

                        if (element.IsSubElement)
                        {
                            element.ParentReferences = sqlWorker.ExecuteQuery<DiagramElementParentReference>(queryGetParentReferences,
                                new SqlParameter("@DiagramType", result.Name),
                                new SqlParameter("@DiagramElement", element.Name)).ToList();
                        }
                    }
                }
                catch (Exception ex)
                {
                    throw new LoadDataException($"Error loading diagram type {key}.", ex);
                }

                return result;
            }
        }

        private static string queryGetDiagramType = @"
            SELECT 
                _pk_Name as Name, 
                Description 
            FROM Table_DiagramType 
            WHERE _pk_Name = @Name
        ";

        private static string queryGetDiagramElements = @"
            SELECT 
                _pk_Name as Name, 
                IsConnector, 
                IsSubElement, 
                _fk_ParentElement as ParentElement 
            FROM Table_DiagramElement 
            WHERE _fk_DiagramType = @DiagramType
        ";

        private static string queryGetDiagramProperties = @"
            SELECT 
                _pk_Name as Name, 
                DataType, 
                IsIdentificationField 
            FROM Table_DiagramElementProperty 
            WHERE 
                _fk_DiagramType = @DiagramType AND 
                _fk_ElementType = @DiagramElement
        ";

        private static string queryGetConnectorReferences = @"
            SELECT 
                _fk_ElementFrom as ElementFrom,
                _fk_ElementTo as ElementTo 
            FROM Table_DiagramElementConnector_Ref 
            WHERE
                _fk_DiagramType = @DiagramType AND 
                _fk_ElementConnector = @DiagramElement
        ";

        private static string queryGetParentReferences = @"
            SELECT 
                _fk_DiagramParentProperty as ParentProperty, 
                _fk_DiagramSubElementProperty as SubElementProperty 
            FROM Table_DiagramElementParent_Ref 
            WHERE
                _fk_DiagramType = @DiagramType AND 
                _fk_DiagramElement = @DiagramElement
        ";

        #region Singleton

        public static IDiagramTypeCache Instance
        {
            get
            {
                return Nested.instance;
            }
        }

        class Nested
        {
            protected Nested()
            { }
            static Nested()
            {
            }

            internal static readonly IDiagramTypeCache instance = new DiagramTypeCache();
        }

        #endregion

    }
}
