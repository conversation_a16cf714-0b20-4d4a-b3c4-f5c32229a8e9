﻿using Codeless.Framework.IO.Standard;
using Codeless.Server.Common.Repository.DataTransferObjects;
using Newtonsoft.Json;
using System.Collections.Generic;

namespace Codeless.Server.Common.Helper
{
    public static class IndexJsonHelper
    {
        public static List<IndexFileItem> ReadIndexJson(this IFileSystem fileSystem, string directoryPath)
        {
            List<IndexFileItem> result = new List<IndexFileItem>();
            string indexFileData = fileSystem.ReadAllText(PathExt.Combine(directoryPath, ArtifactFile.IndexJson));

            if (string.IsNullOrEmpty(indexFileData))
            {
                return result;
            }

            var indexFileItems = JsonConvert.DeserializeObject<IEnumerable<IndexFileItem>>(indexFileData);
            result.AddRange(indexFileItems);

            return result;
        }
    }
}