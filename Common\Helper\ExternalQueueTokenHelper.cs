﻿using Codeless.Framework.ExternalQueue;
using Codeless.Server.Common.Enumerations;

namespace Codeless.Server.Common
{
    public static class ExternalQueueTokenHelper
    {
        public static void ReplaceTokens(ExternalQueue queue, ITokenHelper tokenHelper)
        {
            queue.ConnectionString = tokenHelper.Replace(queue.ConnectionString, Token.ExternalQueueConnection);
            queue.ConnectionString = tokenHelper.Replace(queue.ConnectionString, Token.EnvironmentName);
            queue.ConnectionString = tokenHelper.Replace(queue.ConnectionString, Token.RootUrl);
            queue.ConnectionString = tokenHelper.Replace(queue.ConnectionString, Token.CustomConfig);

            queue.Name = tokenHelper.Replace(queue.Name, Token.EnvironmentName);
            queue.Name = tokenHelper.Replace(queue.Name, Token.RootUrl);
            queue.Name = tokenHelper.Replace(queue.Name, Token.CustomConfig);
        }
    }
}
