﻿namespace Codeless.Server.Common.Cache.CacheBaseClasses
{
    /// <summary>
    /// Cache based on a dictionary of items are accessed by key and that are read just-in-time the first time the are needed.
    /// Loading is done sequentially.
    /// </summary>
    public abstract class DictionaryJITLoadCacheBase<TKey, TValue> : DictionaryCacheBase<TKey, TValue>
    {
        protected abstract TValue LoadDataItem(TKey key);

        protected override void PreLoadData()
        {
            // Do not preload a dictionary, so do nothing here
        }

        protected override TValue GetCacheItem(TKey key)
        {
            EnsureCorrectDataLoaded();

            TValue item = default(TValue);
            bool inCache = false;
            this.lockManager.LockReadMode(() =>
            {
                inCache = this.items.TryGetValue(key, out item);
            });

            if (!inCache)
            {
                this.lockManager.LockUpgradeMode(() =>
                {
                    if (!this.items.TryGetValue(key, out item))
                    {
                        item = this.LoadDataItem(key);

#pragma warning disable S2955 // Generic parameters not constrained to reference types should not be compared to "null"
                        if (item != null)
                        {
                            this.lockManager.LockWriteMode(() =>
                            {
                                items.Add(key, item);
                            });
                        }
#pragma warning restore S2955 // Generic parameters not constrained to reference types should not be compared to "null"
                    }
                });
            }

            return item;
        }

        protected override bool TryGetCacheItem(TKey key, out TValue item)
        {
            item = GetCacheItem(key);
            return true;
        }

        protected bool Contains(TKey key)
        {
            EnsureCorrectDataLoaded();

            return this.lockManager.LockReadMode(() =>
            {
                return this.items.ContainsKey(key);
            });
        }
    }
}