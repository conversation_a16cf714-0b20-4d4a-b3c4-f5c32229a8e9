﻿using Codeless.Framework.EventProcessing;
using Codeless.Framework.EventProcessing.Event;
using Codeless.Framework.EventProcessing.Event.Contracts.ActivityMonitor.Events.ServiceCalls;
using Codeless.Framework.EventProcessing.Event.Contracts.ActivityMonitor.Events.Sessions;
using Codeless.Framework.EventProcessing.Event.Contracts.Heartbeats.Events;
using Codeless.Framework.EventProcessing.Event.Contracts.Reporting.Events;
using Codeless.Framework.EventProcessing.Event.Contracts.TaskMonitor.Events.QueueItems;
using Codeless.Framework.EventProcessing.Event.Contracts.TaskMonitor.Events.Tasks;
using Codeless.Framework.Logging.Standard;
using System;
using System.Collections.Generic;
using System.Threading;

namespace Codeless.Server.Common.EventStore
{
    public class EventsGeneratorWorker
    {
        private readonly ILogger logger;
        private readonly IEventStoreManager eventStoreManager;

        private static Dictionary<EventsGenerationEventType, EventStreamType> EventsMapping = new Dictionary<EventsGenerationEventType, EventStreamType>() {
                { EventsGenerationEventType.ActivityCallCreated, EventStreamType.Activity },
                { EventsGenerationEventType.ActivitySessionCreated, EventStreamType.Activity },
                { EventsGenerationEventType.HeartbeatInfoExecuted, EventStreamType.HeartbeatInfo },
                { EventsGenerationEventType.ReportingPrintRequestCreated, EventStreamType.Reporting },
                { EventsGenerationEventType.ReportingReportJobCreated, EventStreamType.Reporting },
                { EventsGenerationEventType.TaskMonitorQueueItemCreated, EventStreamType.TaskActivity },
                { EventsGenerationEventType.TaskMonitorTaskExecuteCreated, EventStreamType.TaskActivity }
            };

        public EventsGeneratorWorker(ILoggerFactory loggerFactory, IEventStoreManager eventStoreManager)
        {
            loggerFactory = loggerFactory ?? throw new ArgumentNullException(nameof(loggerFactory));
            this.logger = loggerFactory.GetLogger(typeof(EventsGeneratorWorker));
            this.eventStoreManager = eventStoreManager ?? throw new ArgumentNullException(nameof(eventStoreManager));
        }

        public void GenerateEvents(EventsGenerationInfo eventsGenerationInfo)
        {
            if (ConfigSettings.Instance.PublishInstanceHeartbeat)
            {
                try
                {
                    eventsGenerationInfo.Status = EventsGenerationRequestStatus.Generating;

                    Random random = new Random(Environment.TickCount);

                    EventStreamType streamType = GetStreamName(eventsGenerationInfo.EventsType);

                    for (int i = 0; i < eventsGenerationInfo.Count; i++)
                    {
                        (EventBase eventSuccess, EventBase eventFail) = GetEventsBasedOnType(eventsGenerationInfo, random);

                        if (eventSuccess != null)
                        {
                            eventStoreManager.PublishEvent(streamType, eventSuccess);
                        }

                        if (eventFail != null)
                        {
                            if (eventSuccess != null)
                            {
                                Thread.Sleep(GetRandomDelay(eventsGenerationInfo.IntervalMin, eventsGenerationInfo.IntervalMax, random));
                            }

                            eventStoreManager.PublishEvent(streamType, eventFail);
                        }

                        Thread.Sleep(GetRandomDelay(eventsGenerationInfo.IntervalMin, eventsGenerationInfo.IntervalMax, random));
                    }

                    eventsGenerationInfo.Status = EventsGenerationRequestStatus.Completed;
                }
                catch (Exception ex)
                {
                    logger.WriteError("Failed to generate events.", ex);
                }
            }
        }

        private int GetRandomDelay(int min, int max, Random random)
        {
            return random.Next(min, max);
        }

        private EventStreamType GetStreamName(EventsGenerationEventType eventsType)
        {
            if (!EventsMapping.TryGetValue(eventsType, out EventStreamType result))
            {
                throw new ArgumentException("Can't map requested event type to <Codeless.Server.Interfaces.Event.EventStreamType>.", nameof(eventsType));
            }

            return result;
        }

        private (EventBase eventSuccess, EventBase eventFail) GetEventsBasedOnType(EventsGenerationInfo eventsGenerationInfo, Random random)
        {
            EventBase eventSuccess = null;
            EventBase eventFail = null;

            switch (eventsGenerationInfo.EventsType)
            {
                case EventsGenerationEventType.ActivityCallCreated:
                    Guid serviceCallId = Guid.NewGuid();
                    eventSuccess = new ServiceCallStarted() { SessionId = Guid.NewGuid(), ServiceCallId = serviceCallId };
                    eventFail = eventsGenerationInfo.Failed ? new ServiceCallFailed() { ServiceCallId = serviceCallId } : null;
                    break;

                case EventsGenerationEventType.ActivitySessionCreated:
                    eventSuccess = new SessionStarted() { SessionId = Guid.NewGuid(), ClientType = 1 };
                    break;

                case EventsGenerationEventType.HeartbeatInfoExecuted:
                    if (!eventsGenerationInfo.Failed)
                    {
                        eventSuccess = new HeartbeatExecuted() { SourceComponent = SourceComponent.Server, HeartbeatType = HeartbeatType.EventStoreDB, DurationMs = random.Next(100, 200) };
                    }
                    else
                    {
                        eventFail = new HeartbeatFailed() { SourceComponent = SourceComponent.Server, HeartbeatType = HeartbeatType.EventStoreDB };
                    }
                    break;

                case EventsGenerationEventType.ReportingPrintRequestCreated:
                    Guid printRequestId = Guid.NewGuid();
                    eventSuccess = new PrintRequestCreated() { PrintRequestId = printRequestId, PrintRequestQueueId = Guid.NewGuid() };
                    eventFail = eventsGenerationInfo.Failed ? new PrintRequestFailed() { PrintRequestId = printRequestId } : null;
                    break;

                case EventsGenerationEventType.ReportingReportJobCreated:
                    Guid jobId = Guid.NewGuid();
                    eventSuccess = new ReportJobCreated() { JobId = jobId };
                    eventFail = eventsGenerationInfo.Failed ? new ReportJobFailed() { JobId = jobId } : null;
                    break;

                case EventsGenerationEventType.TaskMonitorQueueItemCreated:
                    string queueItemId = Guid.NewGuid().ToString();
                    eventSuccess = new QueueItemCreated() { QueueItemId = queueItemId, TaskExecutionId = Guid.NewGuid(), ServiceCallId = Guid.NewGuid() };
                    eventFail = eventsGenerationInfo.Failed ? new QueueItemCompletedFailed() { QueueItemId = queueItemId } : null;
                    break;

                case EventsGenerationEventType.TaskMonitorTaskExecuteCreated:
                    Guid id = Guid.NewGuid();
                    eventSuccess = new TaskExecutionRequestCreated() { TaskExecutionId = id };
                    break;

                default:
                    throw new ArgumentException("Invalid EventsGenerationInfo parameter.", nameof(eventsGenerationInfo));
            }

            return (eventSuccess, eventFail);
        }
    }
}