﻿using System.Collections.Generic;

namespace Codeless.Server.Common.Cache.Diagrams
{
    public class DiagramType
    {
        public string Name { get; set; }
        public string Description { get; set; }

        public List<DiagramElement> DiagramElements { get; set; }
    }
    public class DiagramElement
    {
        public string Name { get; set; }
        public bool IsConnector { get; set; }
        public bool IsSubElement { get; set; }
        public string ParentElement { get; set; }
        public List<DiagramElementProperty> ElementProperties { get; set; }
        public List<DiagramElementConnectorReference> ConnectorReferences { get; set; }
        public List<DiagramElementParentReference> ParentReferences { get; set; }
    }

    public class DiagramElementConnectorReference
    {
        public DiagramElementConnectorReference()
        {
        }

        public string ElementFrom { get; set; }
        public string ElementTo { get; set; }
    }

    public class DiagramElementParentReference
    {
        public DiagramElementParentReference()
        {
        }

        public string ParentProperty { get; set; }
        public string SubElementProperty { get; set; }
    }

    public class DiagramElementProperty
    {
        public string Name { get; set; }

        public short DataType { get; set; }

        public bool IsIdentificationField { get; set; }
    }

    public enum DiagramPropertyDataType
    {
        String = 1,
        Integer = 2,
        Decimal = 3,
        DateTime = 4,
        Boolean = 5
    }
}
