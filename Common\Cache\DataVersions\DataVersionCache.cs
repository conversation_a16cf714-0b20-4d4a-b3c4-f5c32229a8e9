﻿using Codeless.Framework.DependencyInjection;
using Codeless.Framework.Logging.Standard;
using Codeless.Framework.Sql.Standard;
using Codeless.Framework.LockManagement;
using Codeless.Server.Common.ProducerConsumerPattern;
using Codeless.Server.MetaClasses;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Codeless.Server.Interfaces;

namespace Codeless.Server.Common.Cache.DataVersions
{
    public abstract class DataVersionCache : BackgroundWorkerBase, IDataVersionCache
    {
        // Dependencies
        protected IConfigSettings configSettings;
        protected ITelemetry telemetry;
        protected ILoggerFactory loggerFactory;
        protected ILockManagerFactory lockManagerFactory;
        private ILockManager lockManager;
        protected IMetaPackage package;
        private ISqlWorkerFactory sqlWorkerFactory;
        private ISqlConnectionManager sqlConnectionManager;
        private IProducerConsumer producerConsumer;

        public delegate void VersionChangedHandler(object sender, EventArgs e);
        public event VersionChangedHandler VersionChanged;
        protected void OnVersionChanged(int classId, DataVersion dataVersion)
        {
            if (this.VersionChanged != null)
            {
                this.VersionChanged(this, new VersionChangedEventArgs(classId, dataVersion));
            }
        }

        private readonly BlockingCollection<SingleDataVersion> batchWriteToDiskJobs = new BlockingCollection<SingleDataVersion>();
        private readonly BlockingCollection<SingleDataVersion> writeToDiskJobs = new BlockingCollection<SingleDataVersion>();
        private static int processedRequests = 0;

        private readonly ConcurrentDictionary<(string concern, int classId), bool> knownDataVersionsKeys = new ConcurrentDictionary<(string concern, int classId), bool>();

        private readonly List<Task> workers = new List<Task>();
        private bool running = false;

        protected override string Name => nameof(DataVersionCache);

        protected override void DoWork()
        {
            running = true;
            workers.Add(Task.Factory.StartNew(this.WriteVersions, TaskCreationOptions.LongRunning));

            if (configSettings.DataVersionsIntervalToWriteToDiskSecond > 0)
            {
                workers.Add(Task.Factory.StartNew(this.BatchVersions, TaskCreationOptions.LongRunning));
                workers.Add(Task.Factory.StartNew(this.FinalizeBatch, TaskCreationOptions.LongRunning));
            }
        }

        protected override List<Task> StopWork()
        {
            running = false;
            writeToDiskJobs.CompleteAdding();
            batchWriteToDiskJobs.CompleteAdding();

            return workers;
        }

        public Dictionary<int, DataVersion> GetVersions(string concern, IEnumerable<int> classIds, bool assignConcern = false)
        {
            Dictionary<int, DataVersion> cachedVersions = GetVersionListFromCache(concern, classIds, assignConcern);

            Dictionary<int, DataVersion> notCachedVersions = new Dictionary<int, DataVersion>();

            List<int> classesToReloadFromCache = new List<int>();

            foreach (int classId in classIds)
            {
                if (!cachedVersions.ContainsKey(classId))
                {
                    notCachedVersions.Add(classId, new DataVersion(0, DateTime.Now, assignConcern ? concern : null));
                }

                if (!this.knownDataVersionsKeys.ContainsKey((concern, classId)))
                {
                    IncreaseVersionIfNeededAtFirstLoad(concern, classId, classesToReloadFromCache);
                    this.knownDataVersionsKeys.TryAdd((concern, classId), false);
                }
            }

            if (classesToReloadFromCache.Count > 0)
            {
                Dictionary<int, DataVersion> dataVersionsLoadedFromCache = GetVersionListFromCache(concern, classesToReloadFromCache.ToArray(), assignConcern);

                foreach (KeyValuePair<int, DataVersion> reloadedVersion in dataVersionsLoadedFromCache)
                {
                    cachedVersions[reloadedVersion.Key] = reloadedVersion.Value;

                    if (notCachedVersions.ContainsKey(reloadedVersion.Key))
                    {
                        notCachedVersions.Remove(reloadedVersion.Key);
                    }
                }
            }

            if (notCachedVersions.Count > 0)
            {
                ReadVersions(concern, notCachedVersions);

                foreach (int readClassId in notCachedVersions.Keys)
                {
                    DataVersion readVersion = notCachedVersions[readClassId];
                    cachedVersions.Add(readClassId, readVersion);
                    SetVersionToCache(concern, readClassId, readVersion);
                }
            }

            return cachedVersions;
        }

        private DataVersion ReadVersion(string concernCode, int classId)
        {
            DataVersion dataVersion = null;
            Dictionary<int, DataVersion> dataVersions = new Dictionary<int, DataVersion>();

            dataVersions.Add(classId, new DataVersion(0, DateTime.Now));
            ReadVersions(concernCode, dataVersions);

            DataVersion versionInt;
            if (dataVersions.TryGetValue(classId, out versionInt))
            {
                dataVersion = versionInt;
            }

            return dataVersion;
        }

        private void ReadVersions(string concernCode, Dictionary<int, DataVersion> dataVersions)
        {
            using (IDbConnection connection = sqlConnectionManager.GetNewOpenedSqlConnection(Constants.ALL, false))
            {
                ISqlWorker worker = sqlWorkerFactory.GetSqlWorker(connection);
                string query = string.Format(sqlRead, string.Join(", ", dataVersions.Keys.Select(k => k).ToArray()));

                using (IDataReader reader = worker.ExecuteReader(query, new SqlParameter("ConcernCode", SqlDbType.VarChar) { Value = concernCode }))
                {
                    DataVersion dataVersion;
                    while (reader.Read())
                    {
                        int classId = reader.GetInt32(0);
                        int version = reader.GetInt32(1);
                        DateTime timestamp = reader.GetDateTime(2);

                        dataVersion = new DataVersion(version, timestamp);

                        if (dataVersions.ContainsKey(classId))
                        {
                            dataVersions[classId] = dataVersion;
                        }
                        else
                        {
                            dataVersions.Add(classId, dataVersion);
                        }
                    }
                }
            }
        }

        public void IncreaseVersion(string concern, int classId, DataVersion fixedDataVersion = null)
        {
            AddDataVersionUpdateRequest(concern, classId, fixedDataVersion);
        }

        private void WriteVersion(string concern, int classId, DataVersion dataVersion)
        {
            if (!(Instance is DataVersionEventBased))
            {
                SingleDataVersion singleDataVersion = new SingleDataVersion()
                {
                    Concern = concern,
                    ClassId = classId,
                    DataVersion = dataVersion
                };

                if (configSettings.DataVersionsIntervalToWriteToDiskSecond > 0)
                {
                    batchWriteToDiskJobs.AddIfNotAddingCompleted(singleDataVersion);
                }
                else
                {
                    writeToDiskJobs.AddIfNotAddingCompleted(singleDataVersion);
                }
            }
        }

        public void IncreaseVersions(string concern, IEnumerable<int> classIds)
        {
            foreach (int classId in classIds)
            {
                IncreaseVersion(concern, classId);
            }
        }

        /// <summary>
        /// For the specified concern, increases the Data Version of the given class, if it is marked as needing to increase its version on first load.
        /// </summary>
        /// <param name="concern">Concern code to increase the version for.</param>
        /// <param name="classId">Class to be checked if needs to increase its data version the first time it loads.</param>
        /// <param name="affectedClasses">Classed for which the version was increased.</param>
        private void IncreaseVersionIfNeededAtFirstLoad(string concern, int classId, List<int> affectedClasses)
        {
            cClass metaClass = package.GetClass(classId);
            if (metaClass != null && metaClass.IncreaseDataVersionOnFirstAccess)
            {
                IncreaseVersion(concern, classId);
                affectedClasses.Add(classId);
            }
        }

        protected abstract void Initialize();
        protected abstract DataVersion GetVersionFromCache(string concern, int classId, bool assignConcern = false);
        protected virtual Dictionary<int, DataVersion> GetVersionListFromCache(string concern, IEnumerable<int> classIds, bool assignConcern = false)
        {
            Dictionary<int, DataVersion> dataVersions = new Dictionary<int, DataVersion>();

            foreach (int classId in classIds)
            {
                var classConcern = GetClassConcernForDataVersion(concern, classId);
                DataVersion dataVersion = GetVersionFromCache(classConcern, classId, assignConcern);

                if (dataVersion == null)
                {
                    dataVersion = GetVersionFromCache("*", classId, assignConcern);
                }

                if (dataVersion != null)
                {
                    dataVersions.Add(classId, dataVersion);
                }
            }

            return dataVersions;
        }
        protected abstract DataVersion SetVersionToCache(string concern, int classId, DataVersion version, bool publishEvent = false);

        private readonly Dictionary<(int, string), SingleDataVersion> batchedDataVersions = new Dictionary<(int, string), SingleDataVersion>();

        /// <summary>
        /// Consumes data version increase by collecting them into a batch. Another thread will finalize the batch and send them to the queue to the actual write.
        /// </summary>
        internal void BatchVersions()
        {
            if (Thread.CurrentThread.Name == null)
            {
                Thread.CurrentThread.Name = $"Consumer: Data Version Batch Create [{Thread.CurrentThread.ManagedThreadId}]";
            }

            // Using blocking collection: so execution pauses until new item is added.                       
            foreach (SingleDataVersion newVersion in batchWriteToDiskJobs.GetConsumingEnumerable())
            {
                if (newVersion != null)
                {
                    try
                    {
                        ProcessBatchWriteJob(newVersion);
                    }
                    catch (Exception ex)
                    {
                        logger.WriteError($"Failed to process BatchWriteToDiskJob - ClassId: {newVersion.ClassId}, Concern: {newVersion.Concern}, DataVersion: {newVersion.DataVersion?.Version}", ex);
                    }
                }
                else
                {
                    break; // Stop worker if null is passed
                }
            }
        }

        private void ProcessBatchWriteJob(SingleDataVersion newVersion)
        {
            lockManager.LockWriteMode(() =>
            {
                if (batchedDataVersions.TryGetValue((newVersion.ClassId, newVersion.Concern), out SingleDataVersion batchedVersion))
                {
                    if (newVersion.DataVersion.Version > batchedVersion.DataVersion.Version)
                    {
                        // Update batched item
                        batchedVersion.DataVersion.Version = newVersion.DataVersion.Version;
                        batchedVersion.DataVersion.TimeStamp = newVersion.DataVersion.TimeStamp;
                    }
                }
                else
                {
                    // Add to batch
                    batchedDataVersions.Add((newVersion.ClassId, newVersion.Concern), newVersion);
                }
            });
        }

        /// <summary>
        /// At a set interval, send all batches data versions to the queue to write them to disk and start a new batch. 
        /// </summary>
        private void FinalizeBatch()
        {
            if (Thread.CurrentThread.Name == null)
            {
                Thread.CurrentThread.Name = $"Consumer: Data Version Batch Finalize [{Thread.CurrentThread.ManagedThreadId}]";
            }

            while (this.running)
            {
                Thread.Sleep(configSettings.DataVersionsIntervalToWriteToDiskSecond * 1000);

                try
                {
                    CloseBatch();
                }
                catch (Exception ex)
                {
                    this.logger.WriteError(ex.Message, ex);
                }
            }

            // When shutting down, close the last batch after waiting one last interval time.
            Thread.Sleep(configSettings.DataVersionsIntervalToWriteToDiskSecond * 1000);
            CloseBatch();
        }

        /// <summary>
        /// Release the bath and create a new one (if there are any items in the batch).
        /// </summary>
        private void CloseBatch()
        {
            int? logAmount = null;

            lockManager.LockWriteMode(() =>
            {
                if (batchedDataVersions.Count > 0)
                {
                    logAmount = batchedDataVersions.Count;
                    foreach (SingleDataVersion writeJob in batchedDataVersions.Values)
                    {
                        writeToDiskJobs.AddIfNotAddingCompleted(writeJob);
                    }
                    batchedDataVersions.Clear();
                }
            });

            // Log outside the writer
            if (logAmount.HasValue && this.logger.IsDebugEnabled)
            {
                this.logger.WriteDebug($"Closed batch of {logAmount.Value} data versions.");
            }
        }

        internal void WriteVersions()
        {
            if (Thread.CurrentThread.Name == null)
            {
                Thread.CurrentThread.Name = "Worker: Data Versions DB Writer";
            }

            // Using blocking collection: so execution pauses until new item is added.                       
            foreach (SingleDataVersion job in writeToDiskJobs.GetConsumingEnumerable())
            {
                if (job != null)
                {
                    try
                    {
                        using (IDbConnection connection = sqlConnectionManager.GetNewOpenedSqlConnection(Constants.ALL, false))
                        {
                            ISqlWorker worker = sqlWorkerFactory.GetSqlWorker(connection);

                            List<SqlParameter> parameters = new List<SqlParameter>();
                            parameters.Add(new SqlParameter("ClassId", SqlDbType.Int, 8) { Value = job.ClassId });
                            parameters.Add(new SqlParameter("ConcernCode", SqlDbType.VarChar, 2) { Value = job.Concern });
                            parameters.Add(new SqlParameter("DataVersion", SqlDbType.Int, 8) { Value = job.DataVersion.Version });
                            parameters.Add(new SqlParameter("Timestamp", SqlDbType.DateTime, 8) { Value = DateTime.Now });

                            try
                            {
                                worker.ExecuteCommand(sqlUpdate, parameters.ToArray());
                            }
                            catch (Exception ex)
                            {
                                logger.WriteWarning(string.Format("Failed to increase data version for class id '{0}' - concern '{1}'. ", job.ClassId, job.Concern), ex);
                            }

                            if (configSettings.ApplicationInsightsEnabled)
                            {
                                telemetry.TrackTelemetryEvent("Class data version increased.", new TelemetryProperties(TelemetryActionType.DataVersionCacheOperation, job.ClassId.ToString(), null, null), null);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        logger.WriteError($"Failed to process BatchWriteToDiskJob - ClassId: {job.ClassId}, Concern: {job.Concern}, DataVersion: {job.DataVersion?.Version}", ex);
                    }
                }
                else
                {
                    break; // Stop worker if null is passed
                }
            }
        }
        public DataVersionUpdaterInfo GetDataVersionsUpdaterInfo()
        {
            return new DataVersionUpdaterInfo
            {
                MaxWorkers = configSettings.DataVersionUpdaterWorkers,
                Workers = producerConsumer.GetWorkerCount<DataVersionUpdateRequest>(),
                QueuedUpdates = producerConsumer.GetRequestCount<DataVersionUpdateRequest>(),
                ProcessedRequests = processedRequests
            };
        }

        protected string GetClassConcernForDataVersion(string concern, int classId)
        {
            var metaClass = package.GetClass(classId);

            if (concern != "*" && 
                metaClass != null && 
                metaClass.Persistence == PersistenceLevel.System)
            {
                return "*";
            }

            return MetaClassesHelpers.ResolveDataVersionsConcern(concern);
        }

        private void ProcessDataVersionRequest(DataVersionUpdateRequest request)
        {
            string concern = GetClassConcernForDataVersion(request.Concern, request.ClassId);
            int classId = request.ClassId;
            var fixedDataVersion = request.FixedDataVersion;

            if (fixedDataVersion != null)
            {
                SetVersionToCache(concern, classId, fixedDataVersion);
            }
            else
            {
                DataVersion dataVersion = GetVersionFromCache(concern, classId);

                if (dataVersion == null)
                {
                    dataVersion = ReadVersion(concern, classId);

                    if (dataVersion == null)
                    {
                        dataVersion = new DataVersion(1, DateTime.Now);
                    }
                }

                if (dataVersion.Version < int.MaxValue)
                {
                    dataVersion.Version++;
                }
                else
                {
                    dataVersion.Version = 1;
                }

                dataVersion.TimeStamp = DateTime.Now;
                dataVersion = SetVersionToCache(concern, classId, dataVersion, true);

                if (dataVersion != null)
                {
                    OnVersionChanged(classId, dataVersion);
                    WriteVersion(concern, classId, dataVersion);
                }
                processedRequests++;
            }
        }

        private void ProcessDataVersionRequestError(Exception ex, DataVersionUpdateRequest request)
        {
            logger.WriteError($"Data version update failed: {ex.Message}", ex);
        }

        private void AddDataVersionUpdateRequest(string concern, int classId, DataVersion fixedDataVersion)
        {
            producerConsumer.AddRequest(new DataVersionUpdateRequest() { Concern = concern, ClassId = classId, FixedDataVersion = fixedDataVersion });
        }


        private const string sqlRead = @" SELECT [ClassId], [DataVersion], [Timestamp]
                                          FROM [T005DataVersions] 
                                          WHERE [ClassId] IN ({0}) AND [ConcernCode] in (@ConcernCode, '*')";

        private const string sqlUpdate = @" IF EXISTS 
                                                (SELECT 1 FROM [T005DataVersions] WHERE [ClassId] = @ClassId AND [ConcernCode] = @ConcernCode) 
                                                  UPDATE [T005DataVersions] SET [DataVersion] = @DataVersion, [Timestamp] = @Timestamp  WHERE [ClassId] = @ClassId AND [ConcernCode] = @ConcernCode 
                                            ELSE
                                                INSERT INTO [T005DataVersions] 
                                                    ([ConcernCode], [ClassId], [DataVersion], [Timestamp]) 
                                                VALUES (@ConcernCode, @ClassId, @DataVersion, @Timestamp)";

        [InjectionMethod]
        public void Initialize(IConfigSettings configSettings,
                               ITelemetry telemetry,
                               ILoggerFactory loggerFactory,
                               ILockManagerFactory lockManagerFactory,
                               IMetaPackage package,
                               ISqlWorkerFactory sqlWorkerFactory,
                               ISqlConnectionManager sqlConnectionManager,
                               IProducerConsumer producerConsumer)
        {
            this.configSettings = configSettings ?? throw new ArgumentNullException(nameof(configSettings));
            this.telemetry = telemetry ?? throw new ArgumentNullException(nameof(telemetry));
            this.loggerFactory = loggerFactory ?? throw new ArgumentNullException(nameof(loggerFactory));
            this.lockManagerFactory = lockManagerFactory ?? throw new ArgumentNullException(nameof(lockManagerFactory));
            this.package = package ?? throw new ArgumentNullException(nameof(package));
            this.sqlWorkerFactory = sqlWorkerFactory ?? throw new ArgumentNullException(nameof(sqlWorkerFactory));
            this.sqlConnectionManager = sqlConnectionManager ?? throw new ArgumentNullException(nameof(sqlConnectionManager));
            this.producerConsumer = producerConsumer ?? throw new ArgumentNullException(nameof(producerConsumer));

            this.logger = loggerFactory.GetLogger(this.GetType());
            this.lockManager = lockManagerFactory.GetLockManager(typeof(DataVersionCache));

            producerConsumer.RegisterChannel<DataVersionUpdateRequest>(
                "Data Versions Updater",
                ProcessDataVersionRequest,
                ProcessDataVersionRequestError,
                minWorkers: 0,
                configSettings.DataVersionUpdaterWorkers);

            this.Initialize();
        }

        #region Singleton

        protected DataVersionCache() { }

        public static IDataVersionCache Instance { get; private set; }

        public static void InitializeInstance(IConfigSettings configSettings)
        {
            if (configSettings.UseEventStoreDataVersions)
            {
                Instance = new DataVersionEventBased();
            }
            else if (configSettings.HttpHandlersRedisUseDataVersions)
            {
                Instance = new DataVersionCacheRedis();
            }
            else
            {
                Instance = new DataVersionCacheInstance();
            }
        }

        #endregion
    }

    public class VersionChangedEventArgs : EventArgs
    {
        public int ClassId { get; private set; }
        public DataVersion DataVersion { get; private set; }

        public VersionChangedEventArgs(int classId, DataVersion dataVersion)
        {
            this.ClassId = classId;
            this.DataVersion = dataVersion;
        }
    }

    [Serializable]
    public class DataVersion
    {
        public int Version { get; set; }
        public DateTime TimeStamp { get; set; }
        public string Concern { get; set; }

        public DataVersion() { }

        public DataVersion(int version, DateTime timestamp, string concern = null)
        {
            this.Version = version;
            this.TimeStamp = timestamp;
            this.Concern = concern;
        }
    }

    internal class SingleDataVersion
    {
        internal string Concern { get; set; }
        internal int ClassId { get; set; }
        internal DataVersion DataVersion { get; set; }
    }
}