﻿using Codeless.Framework.DependencyInjection;
using Codeless.Framework.LockManagement;
using Codeless.Framework.Logging.Standard;
using Codeless.Framework.Sql.Standard;
using Codeless.Server.Common.Cache.CacheBaseClasses;
using Codeless.Server.Common.Cache.CacheVersions;
using Codeless.Server.Common.ConnectionInformation;
using Codeless.Server.Common.Enumerations;
using Codeless.Server.Interfaces.Common.Cache;
using System.Collections.Generic;
using System.Data;
using System.Linq;

namespace Codeless.Server.Common.Cache.Events
{
    public class WebhookCache : DictionaryCacheBase<int, List<Webhook>>, IWebhookCache
    {
        private readonly Dictionary<string, int> eventIdsByName = new Dictionary<string, int>();

        private ISqlConnectionManager sqlConnectionManager;
        private ISqlWorkerFactory sqlWorkerFactory;
        private ITokenHelper tokenHelper;

        public List<Webhook> GetWebhooks(string eventType)
        {
            EnsureCorrectDataLoaded();
            if (eventIdsByName.TryGetValue(eventType, out int eventTypeId))
            {
                return GetCacheItem(eventTypeId);
            }
            return new List<Webhook>();
        }

        public List<Webhook> GetWebhooks(int eventTypeId)
        {
            return GetCacheItem(eventTypeId);
        }

        protected override void PreLoadData()
        {
            items.Clear();
            eventIdsByName.Clear();

            List<Webhook> results;
            ConnectionOptions options = new ConnectionOptions()
            {
                Concern = Constants.ALL,
                UseReadOnlyDatabaseIfAvailable = true
            };

            using (IDbConnection connection = sqlConnectionManager.GetNewOpenedSqlConnection(options))
            {
                ISqlWorker worker = sqlWorkerFactory.GetSqlWorker(connection);
                List<Webhook> webhooks = worker.ExecuteQuery<Webhook>(queryWebhooks).ToList();

                foreach (Webhook webhook in webhooks)
                {
                    webhook.URL = tokenHelper.Replace(webhook.URL, Token.BaseUrl);
                    webhook.URL = tokenHelper.Replace(webhook.URL, Token.RootUrl);
                    webhook.URL = tokenHelper.Replace(webhook.URL, Token.CustomConfig);

                    if (!items.TryGetValue(webhook.EventTypeId, out List<Webhook> webhooksPerEventId))
                    {
                        webhooksPerEventId = new List<Webhook>();
                        items.Add(webhook.EventTypeId, webhooksPerEventId);
                    }

                    if (!eventIdsByName.ContainsKey(webhook.EventTypeName))
                    {
                        eventIdsByName.Add(webhook.EventTypeName, webhook.EventTypeId);
                    }

                    webhooksPerEventId.Add(webhook);
                }
            }
        }

        private const string queryWebhooks =
@"select 
	w._pk_id as WebhookId,
    Convert(bigint, t.[OBJECT_ID]) as WebhookObjectId,
	LTrim(RTrim(w.EventTypeName)) as EventTypeName,
	w._fk_EventTypeId as EventTypeId,
    Convert(bigint, t.FK_5270) as EventTypeObjectId,
	LTrim(RTrim(w.RestfulServiceURL)) as URL,
	w.Method,
	w.AuthenticationType,
	LTrim(RTrim(w.AuthenticationURL)) as AuthenticationURL,
	LTrim(RTrim(w.BasicPassword)) as BasicPassword,
	LTrim(RTrim(w.BasicUserId)) as BasicUserId,
	w.BodyType,
	w.HttpHeaders,
	w.InternalCall,
	w.LogCallDetails,
	LTrim(RTrim(w.Office365oAuthClientId)) as Office365oAuthClientId,
	w.Office365oAuthMode,
	LTrim(RTrim(w.Office365oAuthSecret)) as Office365oAuthSecret,
	LTrim(RTrim(w.Office365oAuthTenantId)) as Office365oAuthTenantId,
	LTrim(RTrim(w.PackageGUID)) as PackageGUID,
	LTrim(RTrim(w.UserHashedPassword)) as UserHashedPassword,
	LTrim(RTrim(w.UserId)) as UserId,
	LTrim(RTrim(w._fk_XmlTypeName)) as XmlTypeName,
	w._fk_XmlTypeMajorVersion as XmlTypeMajorVersion
from table_Webhook w
join CP0000005340_ALL t on w.[Object_ID]=t.[Object_ID]
where IsActive=1 and w.company='*' and w.state=1  
";

        [InjectionMethod]
        public void Initialize(IPackageInfo packageInfo,
                               ICacheVersionsCache cacheVersionsCache,
                               ILockManagerFactory lockManagerFactory,
                               ILoggerFactory loggerFactory,
                               ISqlConnectionManager sqlConnectionManager,
                               ISqlWorkerFactory sqlWorkerFactory,
                               ITokenHelper tokenHelper)
        {
            this.sqlConnectionManager = sqlConnectionManager ?? throw new System.ArgumentNullException(nameof(sqlConnectionManager));
            this.sqlWorkerFactory = sqlWorkerFactory ?? throw new System.ArgumentNullException(nameof(sqlWorkerFactory));
            this.tokenHelper = tokenHelper ?? throw new System.ArgumentNullException(nameof(tokenHelper));
            
            base.Initialize(packageInfo, cacheVersionsCache, lockManagerFactory, loggerFactory, CacheVersionTypes.Events);
        }

        #region Singleton

        public static IWebhookCache Instance
        {
            get
            {
                return Nested.instance;
            }
        }

        class Nested
        {
            protected Nested()
            { }
            static Nested()
            {
            }

            internal static readonly IWebhookCache instance = new WebhookCache();
        }

        #endregion

    }
}
