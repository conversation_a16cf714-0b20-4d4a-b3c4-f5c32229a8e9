﻿using Codeless.Framework.IO.Standard;
using Codeless.Framework.Logging.Standard;
using Codeless.Server.Common.Exceptions;
using Codeless.Server.Common.Logging;
using System;
using System.IO;
using System.IO.Compression;
using System.Threading.Tasks;

namespace Codeless.Server.Common
{
    public class FileUtils : IFileUtils
    {
        private readonly ILogger logger;
        private readonly IFileSystem fileSystem;
        private readonly IConfigSettings configSettings;

        public FileUtils()
        {
            logger = LoggerFactorySingleton.Instance?.GetLoggerFactory()?.GetLogger(typeof(FileUtils));
            fileSystem = Storage.Instance.FileSystem;
            configSettings = ConfigSettings.Instance;
        }

        public FileUtils(ILoggerFactory loggerFactory, IFileSystem fileSystem, IConfigSettings configSettings)
        {
            _ = loggerFactory ?? throw new ArgumentNullException(nameof(loggerFactory));
            this.fileSystem = fileSystem ?? throw new ArgumentNullException(nameof(fileSystem));
            this.configSettings = configSettings ?? throw new ArgumentNullException(nameof(configSettings));

            logger = loggerFactory.GetLogger(typeof(FileUtils));
        }

        /// <summary>
        /// Creates a new file path based on input parameters.
        /// </summary>
        /// <param name="fullFilePath">The original file path. The created file path will contain at least the short file name of the original file path.</param>
        /// <param name="extensionIncludingDot">The extension for the created file path. If null, the original extension is used.</param>
        /// <param name="newParentDirectory">The parent directory for the created file path. If null, the original parent is used.</param>
        /// <returns></returns>
        public string GetFilePathWithExtension(string fullFilePath, string extensionIncludingDot = null, string newParentDirectory = null)
        {
            if (null == newParentDirectory)
            {
                newParentDirectory = PathExt.GetDirectoryName(fullFilePath);
            }

            if (null == extensionIncludingDot)
            {
                extensionIncludingDot = PathExt.GetExtension(fullFilePath);
            }

            string fileNameWithoutExtension = PathExt.GetFileNameWithoutExtension(fullFilePath);

            return PathExt.Combine(newParentDirectory, fileNameWithoutExtension + extensionIncludingDot);
        }

        public bool IsFileLocked(string filePath, bool fileMustExist = false)
        {
            try
            {
                if (configSettings.ResourceFileStorageType == Interfaces.ResourceType.Azure)
                {
                    return false;
                }

                // Try to aquire exclusive ownership. If this fails, it means that another process is locking the file,
                // and it cannot be processed yet.

                using (fileSystem.OpenRead(filePath))
                {
                    return false;
                }
            }
            catch (IOException ex) when (!fileMustExist || !(ex is FileNotFoundException))
            {
                return true;
            }
        }

        public void MoveFile(string sourceFileName, string destFileName, bool overwrite = true, bool checkIfExists = false)
        {
            try
            {
                if (checkIfExists && !fileSystem.FileExists(sourceFileName))
                    return;

                if (overwrite)
                {
                    fileSystem.DeleteFile(destFileName);
                }

                try
                {
                    fileSystem.MoveFile(sourceFileName, destFileName);
                }
                catch (IOException ioEx)
                {
                    if (ioEx.Message.Contains("Device or resource busy") &&
                        fileSystem.FileExists(sourceFileName) &&
                        fileSystem.FileExists(destFileName))
                    {
                        // This work-around is needed because on Unix we cannot detect in all cases that a lock is held on the source file.
                        // In such a scenario a File.Move will create the destination file but fail to delete the source file (the move is not transactional).
                        // We need to undo the creation of the copy.
                        fileSystem.DeleteFile(destFileName);
                    }

                    throw;
                }
            }
            catch (Exception ex)
            {
                throw new OperationFailedException($"Error trying to move '{sourceFileName}' to '{destFileName}'. {ex.Message}", ex);
            }
        }

        public void CopyFile(string from, string to, bool overwrite)
        {
            try
            {
                fileSystem.CopyFile(from, to, overwrite);
            }
            catch (Exception ex)
            {
                throw new IOException($"Error trying to copy '{from}' to '{to}'.", ex);
            }
        }

        public void CopyZipWithCheck(string zipFilePath, string destFilePath)
        {
            int maxRetries = 10;
            int i = 0;
            bool done = false;

            while (!done)
            {
                try
                {
                    CopyFile(zipFilePath, destFilePath, true);

                    FileInfo source = new FileInfo(PathExt.GetFullPath(zipFilePath));
                    FileInfo target = new FileInfo(PathExt.GetFullPath(destFilePath));

                    if (target.Exists && target.Length == source.Length)
                    {
                        done = IsValidZip(destFilePath);
                    }
                    else
                    {
                        throw new IOException($"Target file was not created or had a distinct size than the original. Target file: '{destFilePath}'.");
                    }
                }
                catch
                {
                    if (i < maxRetries)
                    {
                        Task.Delay(500).Wait();
                    }
                    else
                    {
                        throw;
                    }
                }

                i++;
            }
        }

        public void DeleteFile(string fileName)
        {
            try
            {
                fileSystem.DeleteFile(fileName);
            }
            catch (Exception ex)
            {
                throw new OperationFailedException($"Error trying to delete '{fileName}'.", ex);
            }
        }

        public void CreateDirectory(string directoryName)
        {
            try
            {
                fileSystem.CreateDirectory(directoryName);
            }
            catch (Exception ex)
            {
                throw new OperationFailedException($"Error trying to create '{directoryName}'.", ex);
            }
        }

        public void WaitLock(string filePath, bool fileMustExist = false)
        {
            int i = 0;

            while (i < configSettings.MaxNumberOfRetriesForLockedFiles &&
                IsFileLocked(filePath, fileMustExist) &&
                configSettings.FileExceptionRetryIntervalsInMilliseconds.TryGetValue(i, out int waitTimeMs))
            {
                i++;

                logger.WriteInfoFormat("Attempt {0} failed on file '{1}'.", i, filePath);

                Task.Delay(waitTimeMs).Wait();
            }

            var fileIsAvailable = i < configSettings.MaxNumberOfRetriesForLockedFiles;

            if (!fileIsAvailable)
            {
                if (!fileSystem.FileExists(filePath))
                {
                    throw new FileNotFoundException($"The file '{filePath}' could not be found.");
                }
                else
                {
                    throw new FileLockedException($"The file '{filePath}' is locked by another process.");
                }
            }
        }

        public void MoveDirectory(string oldDirectory, string newDirectory)
        {
            if (!fileSystem.DirectoryExists(oldDirectory))
            {
                return;
            }

            if (!fileSystem.DirectoryExists(newDirectory))
            {
                fileSystem.CreateDirectory(newDirectory);
            }

            foreach (var file in fileSystem.GetFilesInDirectory(oldDirectory))
            {
                var destinationFile = PathExt.Combine(newDirectory, PathExt.GetFileName(file));
                fileSystem.MoveFile(file, destinationFile);
            }

            var subDirectories = fileSystem.GetDirectories(oldDirectory);

            foreach (var subDirectory in subDirectories)
            {
                var newSubDirectory = PathExt.Combine(newDirectory, PathExt.GetFileName(subDirectory));
                MoveDirectory(subDirectory, newSubDirectory);
            }
        }

        public bool Exists(string filePath) => fileSystem.FileExists(filePath);

        public void WriteAllText(string filePath, string text) => fileSystem.WriteAllText(filePath, text);
        public void WriteAllBytes(string filePath, byte[] bytes) => fileSystem.WriteAllBytes(filePath, bytes);

        public string ReadAllText(string filePath) => fileSystem.ReadAllText(filePath);

        public static bool IsValidZip(string zipFilePath)
        {
            if (string.IsNullOrEmpty(zipFilePath) || !File.Exists(zipFilePath))
            {
                return false;
            }

            try
            {
                using (ZipArchive zipArchive = ZipFile.OpenRead(zipFilePath))
                {
                    // If we can open the archive without an exception, it's valid
                }

                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}
