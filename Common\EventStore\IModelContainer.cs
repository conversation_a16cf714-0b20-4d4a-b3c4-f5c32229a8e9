﻿using Codeless.Framework.EventProcessing.Event;
using Codeless.Framework.EventProcessing.Models;
using System;
using System.Collections.Generic;

namespace Codeless.Server.Common.EventStore
{
    public interface IModelContainer
    {
        event EventHandler StartLeaderWorkEvent;
        event EventHandler StartManagerWorkEvent;
        event EventHandler StopLeaderWorkEvent;
        event EventHandler StopManagerWorkEvent;
        event EventHandler ArtifactModelVersionUpdated;
        Guid InstanceId { get; }
        void OnModelVersionUpdated();
        Dictionary<Guid, InstanceModel> Instances { get; }
        bool IsLeader { get; }
        bool IsManager { get; }
        bool IsLeaderElected { get; }
        bool TryImportModelSnapshot(out List<StreamEventInfo> streamEventInfos);
    }
}
