﻿using Codeless.Framework.DependencyInjection;
using Codeless.Server.CommunicationClasses;
using Codeless.Server.MetaClassesXML;
using System;

namespace Codeless.Server.Common.Cache.ClassPersistence
{
    public class ClassPersistenceCache : IClassPersistenceCache
    {
        private IMetaClassesCache metaClassesCache;

        [InjectionMethod]
        public void Initialize(IMetaClassesCache metaClassesCache)
        {
            this.metaClassesCache = metaClassesCache;
        }

        public PersistencyLevel GetClassPersistence(int classId)
        {
            var classMetadata = metaClassesCache.GetClass(classId);
            return classMetadata == null ? default : ResolvePersistencyLevel(classMetadata);
        }

        public string GetFixedConcernPersistency(int classId, string rununit)
        {
            string result = rununit;
            var classMetadata = metaClassesCache.GetClass(classId);

            if (ResolvePersistencyLevel(classMetadata) == PersistencyLevel.System)
            {
                result =  classMetadata.IsRuntime ? Constants.ALL : Constants.SYS;
            }

            return result;
        }

        public string GetFixedCompanyPersistency(int classId, string company)
        {
            string result = company;
            PersistencyLevel persistencyLevel = GetClassPersistence(classId);
            if (persistencyLevel == PersistencyLevel.System || persistencyLevel == PersistencyLevel.Concern)
            {
                result = Constants.AllCompanyCode;
            }

            return result;
        }

        private static PersistencyLevel ResolvePersistencyLevel(Server.MetaClassesXML.Class cls)
        {
            return (PersistencyLevel)Convert.ToInt32(cls.Persistencylevel.Trim());
        }


        #region Singleton

        public static IClassPersistenceCache Instance
        {
            get
            {
                return Nested.instance;
            }
        }

        class Nested
        {
            protected Nested()
            { }
            static Nested()
            {
            }

            internal static readonly IClassPersistenceCache instance = new ClassPersistenceCache();
        }

        #endregion

    }
}