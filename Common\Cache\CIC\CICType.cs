﻿using Codeless.Server.Common.Enumerations;

namespace Codeless.Server.Common.Cache.CIC
{
    public class CICType
    {
        public string InterfaceKindOf { get; set; }
        public bool InterfaceIsOnCompanyCodeLevel { get; set; }
        public bool OneStepExportProcessingYn { get; set; }
        public bool OneStepImportProcessingYn { get; set; }
        public int ClassIDFunction_UpdateDataFromInterfaceData { get; set; }
        public int ProcessFunctionId_UpdateDataFromInterfaceData { get; set; }
        public int TaskSchedulerId_UpdateDataFromInterfaceData { get; set; }
        public string XMLTypeNameImport { get; set; }
        public int XMLTypeVersionImport { get; set; }
        public string XMLTypeNameExport { get; set; }
        public int XMLTypeVersionExport { get; set; }
        public string ESB_WorkflowAction { get; set; }
        public string ESB_WorkflowName { get; set; }
        public int TaskSchedulerIdUpdateControlObject { get; set; }
        public string LocalSystemName { get; set; }
        public string ConcernId { get; set; }
        public string TypeName { get; set; }
        public string OutboundSubFolder { get; set; }
        public decimal ObjectId { get; set; }
        public bool ExportAsJson { get; set; }
        public bool ExportToFile { get; set; }
        public bool ExportWebhookEvent { get; set; }
        public int ExportWebhookEventTypeId { get; set; }
        public bool ExportAllowCICAddAPI { get; set; }
        public bool ExportAllowCICCreateAPI { get; set; }
        public string ExportCICAddUserGroupName { get; set; }
        public string ExportCICCreateUserGroupName { get; set; }
        public bool ExportDataOnly { get; set; }
        public bool StoreDataInsideCICRecord { get; set; }
        public CompressionType ExportCompression { get; set; }
        public bool ExportToCurrentApplication { get; set; }
        public int IncomingPriority { get; set; }
        public string IncomingConcernCode { get; set; }
    }
}
