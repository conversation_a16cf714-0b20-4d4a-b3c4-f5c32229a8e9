﻿using Codeless.Server.MetaClasses;
using System.Collections.Generic;
using System.Linq;

namespace Codeless.Server.Common.Helper
{
    public static class ApplicationStorageConnectionHelper
    {
        private const string MainConnectionName = "Main";

        public static List<ApplicationStorageConnection> GetAllStorageConnections(IMetaPackage package, string applicationName, List<string> unifiedApplicationNames)
        {
            List<ApplicationStorageConnection> allStorageConnections = package.GetApplicationStorageConnections() ?? new List<ApplicationStorageConnection>();
            ApplicationNameRemoveSpaces(allStorageConnections);

            if (unifiedApplicationNames != null && unifiedApplicationNames.Count > 0)
            {
                var storageConnectionsLookup = allStorageConnections.ToLookup(conn => conn.ApplicationRootName);

                foreach (var unifiedApplicationName in unifiedApplicationNames)
                {
                    string normalizedAppName = unifiedApplicationName?.Replace(" ", "") ?? string.Empty;

                    if (!HasMainConnection(storageConnectionsLookup, normalizedAppName))
                    {
                        allStorageConnections.Add(CreateMainConnection(unifiedApplicationName));
                    }
                    else
                    {
                        foreach (var connection in allStorageConnections.Where(
                            conn => conn.ApplicationRootName == normalizedAppName))
                        {
                            connection.ApplicationRootName = unifiedApplicationName;
                        }
                    }
                }
            }
            else
            {
                UpdateApplicationName(applicationName, allStorageConnections);

                if (!allStorageConnections.Exists(conn => conn.StorageConnectionName == MainConnectionName))
                {
                    allStorageConnections.Add(CreateMainConnection(applicationName));
                }
            }

            return allStorageConnections;
        }

        private static void ApplicationNameRemoveSpaces(List<ApplicationStorageConnection> allStorageConnections)
        {
            foreach (var storageConnection in allStorageConnections)
            {
                if (!string.IsNullOrEmpty(storageConnection.ApplicationRootName))
                {
                    storageConnection.ApplicationRootName = storageConnection.ApplicationRootName.Replace(" ", "");
                }
            }
        }

        private static void UpdateApplicationName(string applicationName, List<ApplicationStorageConnection> allStorageConnections)
        {
            foreach (var storageConnection in allStorageConnections)
            {
                storageConnection.ApplicationRootName = applicationName;
            }
        }

        private static bool HasMainConnection(ILookup<string, ApplicationStorageConnection> lookup, string applicationName)
        {
            var applicationStorageConnections = lookup[applicationName];
            return applicationStorageConnections != null && applicationStorageConnections.Any(conn => conn.StorageConnectionName == MainConnectionName);
        }

        private static ApplicationStorageConnection CreateMainConnection(string applicationName) => new ApplicationStorageConnection
        {
            StorageConnectionName = MainConnectionName,
            IsMainConnection = true,
            ConnectionTypeId = 1,
            StorageConnectionId = 0,
            ApplicationRootName = applicationName
        };
    }
}