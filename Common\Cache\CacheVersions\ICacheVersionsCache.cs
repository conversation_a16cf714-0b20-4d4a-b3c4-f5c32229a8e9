﻿using Codeless.Common;
using Codeless.Server.Interfaces.Common.Cache;
using System;

namespace Codeless.Server.Common.Cache.CacheVersions
{
    public interface ICacheVersionsCache
    {
        int GetCacheVersion(CacheVersionTypes cacheVersionType, bool forceReload = false);
        int GetCacheVersionPersonal(CacheVersionTypes cacheVersionType, string userId, bool forceReload = false);
        VersionInfo GetCacheVersions(bool forceReload = false);
        VersionInfo GetCacheVersionsFromDatabase();
        VersionInfo GetCacheVersionsPersonal(string userId, bool forceReload = false);
        MetadataVersion GetMetadataVersion(bool forceReload = false);
        void UpdateCacheVersion(CacheVersionTypes type, int? version, bool runtimeModelUpdate);
        void UpdateCacheVersionPersonal(string userId, CacheVersionTypes type, int? version, bool runtimeModelUpdate);
        void SetCacheVersionInDatabase(CacheVersionTypes type, int version);
        void RegisterOnCacheVersionChange(CacheVersionTypes type, Action<int> actionOnVersionChange);
    }
}