﻿using Codeless.Framework.Logging.Standard;
using Codeless.Server.Common.ClassesByApplication;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;

namespace Codeless.Server.Common.Helper
{
    public abstract class RelayHelperBase
    {
        protected readonly IConfigSettings configSettings;
        protected readonly ILogger logger;
        protected const int MAX_URL_LENGTH = 2000; // Conservative limit to avoid 414 errors
        protected const string CLASS_IDS_PARAM = "classIds";
        protected const string CLASS_ID_PARAM = "classId";

        protected RelayHelperBase(IConfigSettings configSettings, ILogger logger = null)
        {
            this.configSettings = configSettings ?? throw new ArgumentNullException(nameof(configSettings));
            this.logger = logger;
        }

        protected List<List<int>> ChunkClassIdsByUrlLength(List<int> classIds, string baseUrl)
        {
            if (classIds == null || !classIds.Any())
                return new List<List<int>>();

            var chunks = new List<List<int>>();
            var currentChunk = new List<int>();

            foreach (var classId in classIds)
            {
                var testChunk = new List<int>(currentChunk) { classId };
                var testUrl = BuildUrlWithClassIds(baseUrl, testChunk);

                if (testUrl.Length > MAX_URL_LENGTH && currentChunk.Count != 0)
                {
                    // Current chunk would exceed limit, save it and start new chunk
                    chunks.Add(new List<int>(currentChunk));
                    currentChunk.Clear();
                    currentChunk.Add(classId);
                }
                else
                {
                    currentChunk.Add(classId);
                }
            }

            if (currentChunk.Any())
            {
                chunks.Add(currentChunk);
            }

            return chunks;
        }

        protected string BuildUrlWithClassIds(string baseUrl, List<int> classIds)
        {
            if (classIds == null || !classIds.Any())
                return baseUrl;

            string classIdsValue = string.Join(",", classIds);
            return AppendOrReplaceQueryParam(baseUrl, CLASS_IDS_PARAM, classIdsValue);
        }

        protected string BuildUrlWithSingleClassId(string baseUrl, int classId)
        {
            return AppendOrReplaceQueryParam(baseUrl, CLASS_ID_PARAM, classId.ToString());
        }

        protected string AppendOrReplaceQueryParam(string url, string paramName, string paramValue)
        {
            if (string.IsNullOrEmpty(url) || string.IsNullOrEmpty(paramName))
                return url;

            // Check if parameter already exists
            var regex = new Regex($@"([?&])({Regex.Escape(paramName)}=)([^&]*)", RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(configSettings.RegExTimeoutMs));
            var match = regex.Match(url);
            
            if (match.Success)
            {
                // Replace existing parameter while preserving the separator character (? or &)
                string separator = match.Groups[1].Value;
                return regex.Replace(url, $"{separator}{paramName}={paramValue}");
            }
            else
            {
                // Add new parameter
                string separator = url.Contains('?') ? "&" : "?";
                return $"{url}{separator}{paramName}={paramValue}";
            }
        }

        protected static Dictionary<string, List<int>> GroupClassIdsByApplication(
            List<int> classIds,
            IClassesByApplicationProvider classesByApplicationProvider)
        {
            if (classIds == null || !classIds.Any())
                return new Dictionary<string, List<int>>();

            return classIds
                .GroupBy(classId => classesByApplicationProvider.GetClassById(classId)?.ApplicationName ?? string.Empty)
                .ToDictionary(g => g.Key, g => g.ToList());
        }

        protected void LogError(string message, Exception ex = null)
        {
            logger?.WriteError(message, ex);
        }

        protected void LogWarning(string message)
        {
            logger?.WriteWarning(message);
        }

        protected static List<int> ParseClassIds(string classIdsValue)
        {
            if (string.IsNullOrWhiteSpace(classIdsValue))
                return new List<int>();

            return classIdsValue.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                .Select(id => int.TryParse(id.Trim(), out int parsedId) ? parsedId : (int?)null)
                .Where(id => id.HasValue)
                .Select(id => id.Value)
                .ToList();
        }

        protected static string ExtractClassIdsFromUrl(string url)
        {
            if (string.IsNullOrEmpty(url))
                return string.Empty;

            int questionMarkIndex = url.IndexOf('?');
            if (questionMarkIndex < 0)
                return string.Empty;

            string queryString = url.Substring(questionMarkIndex + 1);
            var queryParams = queryString.Split('&');

            foreach (var param in queryParams)
            {
                string[] keyValue = param.Split('=');
                if (keyValue.Length == 2 && keyValue[0].Equals(CLASS_IDS_PARAM, StringComparison.OrdinalIgnoreCase))
                {
                    return keyValue[1];
                }
            }

            return string.Empty;
        }

        protected virtual void ExecuteRelayRequest(string url, string appName, Dictionary<string, object> additionalParams = null)
        {
            // Default implementation - can be overridden by derived classes if needed
        }
    }
}
