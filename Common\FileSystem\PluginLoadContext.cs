﻿using Codeless.Framework.IO.Standard;
using System.Collections.Concurrent;
using System.Reflection;
using System.Runtime.Loader;

namespace Codeless.Server.Common.FileSystem
{
    public class PluginLoadContext : AssemblyLoadContext
    {
        private readonly AssemblyDependencyResolver _resolver;
        private static readonly ConcurrentDictionary<string, PluginLoadContext> _pluginContexts = new ConcurrentDictionary<string, PluginLoadContext>();
        private readonly IFileSystem fileSystem;
        private PluginLoadContext(string pluginPath, IFileSystem fileSystem) : base(isCollectible: false)
        {
            this.fileSystem = fileSystem;
            _resolver = new AssemblyDependencyResolver(pluginPath);
        }

        protected override Assembly Load(AssemblyName assemblyName)
        {
            // Check for assemblies already loaded
            Assembly assembly = AppDomain.CurrentDomain.GetAssemblies().FirstOrDefault(a => a.FullName == assemblyName.FullName);
            if (assembly != null)
            {
                return assembly;
            }

            // Resolve the assembly path using AssemblyDependencyResolver
            string assemblyPath = _resolver.ResolveAssemblyToPath(assemblyName);
            if (assemblyPath != null)
            {
                var assemblyBytes = this.fileSystem.ReadAllBytes(assemblyPath);
                using (MemoryStream ms = new MemoryStream(assemblyBytes))
                {
                    return LoadFromStream(ms);
                }
            }

            // Fallback to default context if the assembly cannot be resolved
            return null;
        }

        protected override IntPtr LoadUnmanagedDll(string unmanagedDllName)
        {
            // Resolve the unmanaged DLL path using AssemblyDependencyResolver
            string libraryPath = _resolver.ResolveUnmanagedDllToPath(unmanagedDllName);
            if (libraryPath != null)
            {
                return LoadUnmanagedDllFromPath(libraryPath);
            }

            // Fallback to default context if the unmanaged DLL cannot be resolved
            return IntPtr.Zero;
        }
       
        public static PluginLoadContext GetOrCreateContext(string pluginPath,IFileSystem fileSystem)
        {
            return _pluginContexts.GetOrAdd(pluginPath, path => new PluginLoadContext(path, fileSystem));
        }

    }
}
