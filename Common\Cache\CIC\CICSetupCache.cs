﻿using Codeless.Framework.Sql.Standard;
using Codeless.Framework.LockManagement;
using Codeless.Server.Common.Cache.CacheBaseClasses;
using Codeless.Server.Common.Cache.CacheVersions;
using Codeless.Server.Common.ConnectionInformation;
using Codeless.Server.Interfaces.Common.Cache;
using System.Data;
using Codeless.Framework.DependencyInjection;
using Codeless.Framework.Logging.Standard;
using System;

namespace Codeless.Server.Common.Cache.CIC
{
    public class CICSetupCache : DictionaryCacheBase<string, CICSetup>, ICICSetupCache
    {
        private readonly ISqlWorkerFactory sqlWorkerFactory = new SqlWorkerFactory();
        private ISqlConnectionManager sqlConnectionManager;

        [InjectionMethod]
        public void Initialize(IPackageInfo packageInfo,
                               ICacheVersionsCache cacheVersionsCache,
                               ILockManagerFactory lockManagerFactory,
                               ILoggerFactory loggerFactory,
                               ISqlConnectionManager sqlConnectionManager)
        {

            base.Initialize(packageInfo, cacheVersionsCache, lockManagerFactory, loggerFactory, CacheVersionTypes.CICSetup);
            this.sqlConnectionManager = sqlConnectionManager ?? throw new ArgumentNullException(nameof(sqlConnectionManager));
        }

        public CICSetup GetCICSetup(string concern)
        {
            return GetCacheItem(concern);
        }

        protected override void PreLoadData()
        {
            items.Clear();

            ConnectionOptions options = new ConnectionOptions()
            {
                Concern = Constants.ALL,
                UseReadOnlyDatabaseIfAvailable = true
            };

            using (IDbConnection connection = sqlConnectionManager.GetNewOpenedSqlConnection(options))
            {
                ISqlWorker worker = sqlWorkerFactory.GetSqlWorker(connection);

                foreach (CICSetup item in worker.ExecuteQuery<CICSetup>(queryGetInterfaceSetup))
                {
                    if (!items.ContainsKey(item.ConcernId))
                    {
                        items.Add(item.ConcernId, item);
                    }
                    else
                    {
                        logger.WriteWarning($"Duplicate CIC Settup found for concern {item.ConcernId}.");
                    }
                }
            }
        }

        private const string queryGetInterfaceSetup =
            @"             
            SELECT [T5180].[ESB_Systemname_PathFolderName] AS PathFolderName, 
                LTRIM(RTRIM([T5180].[ESB_SystemName])) AS EsbSystemName,
                [T5180].[ScanForCICFiles] AS FileScanEnabled,
            	[T5180].[AllowAPICalls] AS [AllowAPICalls],
                LTRIM(RTRIM([T5180].[_pk_LocalSystemname])) AS LocalSystemName,
                LTRIM(RTRIM([T5180].[_pk_ConcernId])) AS ConcernId,
                TaskSchedulerId_UpdateControlObject_Incoming as TaskSchedulerIdIncoming,
                ProcessFunctionId_UpdateControlObject_incoming as ProcessFunctionIdIncoming
            FROM [Table_CIC_InterfaceSetUp] T5180
            ";


        #region Singleton

        public static ICICSetupCache Instance
        {
            get
            {
                return Nested.instance;
            }
        }

        class Nested
        {
            protected Nested()
            { }
            static Nested()
            {
            }

            internal static readonly ICICSetupCache instance = new CICSetupCache();
        }

        #endregion

    }
}