﻿using Codeless.Framework.DependencyInjection;
using Codeless.Framework.EventProcessing;
using Codeless.Framework.EventProcessing.Event;
using Codeless.Framework.EventProcessing.Event.Contracts.Versions;
using Codeless.Framework.Logging.Standard;
using Codeless.Framework.Sql.Standard;
using Codeless.Framework.LockManagement;
using Codeless.Server.Common.ConnectionInformation;
using Codeless.Server.Common.EventStore;
using Codeless.Server.Interfaces.Common.Cache;
using System;

namespace Codeless.Server.Common.Cache.CacheVersions
{
    public class CacheVersionsEventBased : CacheVersionsBase
    {
        private IEventStoreManager eventStoreManager;
        public override VersionInfo GetCacheVersions(bool forceReload = false)
        {
            VersionInfo result = null;

            lockManagerGlobal.LockReadMode(() =>
            {
                result = this.Versions;
            });

            return result;
        }

        public override int GetCacheVersion(CacheVersionTypes cacheVersionType, bool forceReload = false)
        {
            if (cacheVersionType == CacheVersionTypes.Metadata)
            {
                throw new ArgumentException("Use GetMetadata to obtain the metadata version.", "cacheVersionType");
            }

            int result = GetGlobalCacheVersion(cacheVersionType);

            return result;
        }

        public override void UpdateCacheVersion(CacheVersionTypes type, int? version, bool runtimeModelUpdate)
        {
            if (version == null)
            {
                UpdateCacheVersionInDatabase(type);

                lockManagerGlobal.LockWriteMode(() =>
                {
                    GetCacheVersionsFromDatabase(this.Versions );
                });

                int currentVersion = GetCacheVersion(type);
                PublishCacheVersionIncreasedEvent(type, currentVersion);
            }
            else
            {                
                int currentVersion = type == CacheVersionTypes.Metadata ? GetMetadataVersion(false).ToInt() :  GetCacheVersion(type);

                if (currentVersion < version || version == 1)
                {
                    CacheVersionDto cacheVersionDto = new CacheVersionDto()
                    {
                        CacheType = type,
                        Version = Convert.ToInt32(version)
                    };

                    VersionInfo currentVersionInfo = GetCacheVersions();
                    FillVersionInfo(currentVersionInfo, cacheVersionDto);

                    lockManagerGlobal.LockWriteMode(() =>
                    {
                        AssignNewVersions(currentVersionInfo);
                    });

                    if (runtimeModelUpdate)
                    {
                        PublishCacheVersionIncreasedEvent(type, Convert.ToInt32(version));
                    }
                }
            }
        }

        public override VersionInfo GetCacheVersionsPersonal(string userId, bool forceReload = false)
        {
            VersionInfo result = null;

            EnsureCacheVersionsPersonalLoaded(userId);

            this.GetPersonalLockManager(userId).LockReadMode(() =>
            {
                this.UserVersions.TryGetValue(userId, out result);
            });

            return result;
        }

        public override int GetCacheVersionPersonal(CacheVersionTypes cacheVersionType, string userId, bool forceReload = false)
        {
            int result = 0;
            VersionInfo versionInfo = null;

            EnsureCacheVersionsPersonalLoaded(userId);

            this.GetPersonalLockManager(userId).LockReadMode(() =>
            {
                if (this.UserVersions.TryGetValue(userId, out versionInfo) && versionInfo != null)
                {
                    result =  PersonalCacheversionsSearch[cacheVersionType].Invoke(versionInfo);
                }
            });

            return result;
        }

        public override void UpdateCacheVersionPersonal(string userId, CacheVersionTypes type, int? version, bool runtimeModelUpdate)
        {
            if (!string.IsNullOrWhiteSpace(userId))
            {
                if (version == null)
                {
                    UpdateCacheVersionPersonalInDatabase(userId, type);
                    EnsureCacheVersionsPersonalLoaded(userId, true);
                    int currentVersion = GetCacheVersionPersonal(type, userId);
                    PublishPersonalCacheVersionIncreasedEvent(userId, type, currentVersion);
                }
                else
                {
                    int currentVersion = GetCacheVersionPersonal(type, userId);

                    if (currentVersion < version || version == 1)
                    {
                        CacheVersionDto cacheVersionDto = new CacheVersionDto()
                        {
                            CacheType = type,
                            Version = Convert.ToInt32(version)
                        };

                        VersionInfo currentVersionInfo = GetCacheVersionsPersonal(userId);
                        FillVersionInfo(currentVersionInfo, cacheVersionDto);

                        this.GetPersonalLockManager(userId).LockWriteMode(() =>
                        {
                            this.UserVersions[userId] =  currentVersionInfo;
                        });
                    }

                    if (runtimeModelUpdate)
                    {
                        PublishPersonalCacheVersionIncreasedEvent(userId, type, Convert.ToInt32(version));
                    }
                }
            }
        }

        [InjectionMethod]
        public void Initialize(ILoggerFactory loggerFactory, ILockManagerFactory lockManagerFactory, IPackageInfo packageInfo, IConfigSettings configSettings, ISqlWorkerFactory sqlWorkerFactory, ISqlConnectionManager sqlConnectionManager, IEventStoreManagerSingleton eventStoreManagerSingleton)
        {
            InitializeCache(loggerFactory, lockManagerFactory, packageInfo, configSettings, sqlWorkerFactory, sqlConnectionManager);
            GetCacheVersionsFromDatabase(this.Versions);
            eventStoreManager = eventStoreManagerSingleton.GetEventStoreManager();
        }

        private void EnsureCacheVersionsPersonalLoaded(string userId, bool loadNeeded = false)
        {
            if (!string.IsNullOrWhiteSpace(userId))
            {
                if (!loadNeeded)
                {
                    GetPersonalLockManager(userId).LockReadMode(() =>
                    {
                        if (!this.UserVersions.TryGetValue(userId, out VersionInfo currentVersion))
                        {
                            loadNeeded = true;
                        }
                    });
                }

                if (loadNeeded)
                {
                    this.GetPersonalLockManager(userId).LockWriteMode(() =>
                    {
                        this.UserVersions[userId] =  GetVersionsPersonalFromDatabase(userId);
                    });
                }
            }

        }

        private void PublishPersonalCacheVersionIncreasedEvent(string userId, CacheVersionTypes type, int version)
        {
            PersonalCacheVersionIncreased personalCacheVersionIncreased = new PersonalCacheVersionIncreased
            {
                CacheType = type,
                Version = version,
                UserName = userId
            };

            eventStoreManager.PublishEvent(EventStreamType.CacheVersions, personalCacheVersionIncreased);
        }

        private void PublishCacheVersionIncreasedEvent(CacheVersionTypes type, int version)
        {
            CacheVersionIncreased cacheVersionIncreased = new CacheVersionIncreased
            {
                CacheType = type,
                Version = version,
            };

            eventStoreManager.PublishEvent(EventStreamType.CacheVersions, cacheVersionIncreased);
        }
    }
}
