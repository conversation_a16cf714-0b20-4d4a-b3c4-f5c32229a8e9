﻿using System.Text;
using System.Xml;

namespace Codeless.Server.Common
{
    public partial class XmlHelper
    {
        /// <summary>
        /// Serializes a Dto object and stores the data in an XmlDocument.
        /// </summary>
        /// <typeparam name="TDto">The type of the Dto to serialize.</typeparam>
        /// <param name="dtoObject">The actual Dto object.</param>
        /// <returns>An XmlDocument containing the serialized Dto data.</returns>
        public static XmlDocument Serialize<TDto>(TDto dtoObject, bool useDataContracts = false) where TDto : class
        {
            if (dtoObject is XmlDocument)
            {
                return dtoObject as XmlDocument;
            }

            SerializeWrapper xmlSerializer = new SerializeWrapper(typeof(TDto), useDataContracts);
            string data = xmlSerializer.SerializeData(dtoObject);

            XmlDocument doc = new XmlDocument();
            
            // Protect against XXE vulnerabilities by disabling external entity processing
            doc.XmlResolver = null;

            doc.LoadXml(data);

            return doc;
        }

        /// <summary>
        /// Serializes a Dto object and stores the data in a string.
        /// </summary>
        /// <typeparam name="TDto">The type of the Dto to serialize.</typeparam>
        /// <param name="dtoObject">The actual Dto object.</param>
        /// <returns>An xml string containing the serialized Dto data.</returns>
        public static string SerializeToString<TDto>(TDto dtoObject, bool useDataContracts = false) where TDto : class
        {
            SerializeWrapper xmlSerializer = new SerializeWrapper(typeof(TDto), useDataContracts);
            return xmlSerializer.SerializeData(dtoObject);
        }

        public static string SerializeObjectToString(object dtoObject, bool useDataContracts = false)
        {
            SerializeWrapper xmlSerializer = new SerializeWrapper(dtoObject.GetType(), useDataContracts);
            return xmlSerializer.SerializeData(dtoObject);
        }

        /// <summary>
        /// This method is responsible for deserializing an XmlDocument to a dto.
        /// </summary>
        /// <typeparam name="TDto">The type of the Dto that we need to deserialize to.</typeparam>
        /// <param name="doc">The XmlDocument containing the serialized Dto data.</param>
        /// <returns>The deserialized Dto object.</returns>
        public static TDto Deserialize<TDto>(XmlDocument doc, bool useDataContracts = false) where TDto : class
        {
            SerializeWrapper xmlSerializer = new SerializeWrapper(typeof(TDto), useDataContracts);
            return xmlSerializer.DeserializeData(doc.OuterXml) as TDto;
        }

        /// <summary>
        /// This method is responsible for deserializing an xml string to a dto.
        /// </summary>
        /// <typeparam name="TDto">The type of the Dto that we need to deserialize to.</typeparam>
        /// <param name="xml">The xml string containing the serialized Dto data.</param>
        /// <returns>The deserialized Dto object.</returns>
        public static TDto Deserialize<TDto>(string xml, bool useDataContracts = false) where TDto : class
        {
            SerializeWrapper xmlSerializer = new SerializeWrapper(typeof(TDto), useDataContracts);
            return xmlSerializer.DeserializeData(xml) as TDto;
        }

        /// <summary>
        /// To convert a Byte Array of Unicode values (UTF-8 encoded) to a complete String.
        /// </summary>
        /// <param name="characters">Unicode Byte Array to be converted to String</param>
        /// <returns>String converted from Unicode Byte Array</returns>
        public static string ByteArrayToUTF8String(byte[] characters)
        {
            UTF8Encoding encoding = new UTF8Encoding();
            string constructedString = encoding.GetString(characters);
            return (constructedString);
        }
        /// <summary>
        /// Converts the String to UTF8 Byte array and is used in De serialization
        /// </summary>
        /// <param name="stringVal"></param>
        /// <returns></returns>
        public static byte[] StringToUTF8ByteArray(string stringVal)
        {
            UTF8Encoding encoding = new UTF8Encoding();
            byte[] byteArray = encoding.GetBytes(stringVal);
            return byteArray;
        }
    }
}