﻿using System.Data;

namespace Codeless.Server.Common.Cache.Counters
{
    public interface ICountersCache
    {
        bool CounterRecordExists(string name);
        void CreateCounterObject(IDbConnection connection, IDbTransaction transaction, CounterKey counterKey);
        void CreateCounterObject(IDbConnection connection, IDbTransaction transaction, string name);
        void EnsureCounterRecordExists(IDbConnection connection, IDbTransaction transaction, string name);
        void EnsureSequenceExists(string concernCode, string name, long startingValue);
        long GetNextCounterRecordValue(IDbConnection connection, IDbTransaction transaction, string name);
        long GetNextSequenceValue(IDbConnection connection, IDbTransaction transaction, string name);
        long GetNextValueForCounterOrSequence(IDbConnection connection, IDbTransaction transaction, string concernCode, string name);
        bool SequenceExists(string name);
    }
}