﻿using System.Collections.Generic;

namespace Codeless.Server.Common.Cache.AttachmentLocation
{
    public class CustomAttachmentLocation
    {
        public int ClassId { get; set; }
        public string AttachmentLocation { get; set; }
        public bool DeleteFilesFromAttachmentAttributes { get; set; }
        public bool DisableAttachmentsServiceCache { get; set; }
        public bool DeleteAllAttachmentsOnObjectDelete { get; set; }
        public List<int> TokenAttributeIds { get; set; }
    }
}