﻿using Codeless.Framework.DependencyInjection;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using System.Security.Cryptography;

namespace Codeless.Server.Common.ArtifactsSigning
{
    public class ArtifactsPublicKeyProvider : IArtifactsPublicKeyProvider
    {
        private readonly Dictionary<int, RSA> publicKeys = new Dictionary<int, RSA>();

        public RSA GetPublicKey(int keyVersion)
        {
            if (publicKeys.TryGetValue(keyVersion, out RSA rsa))
            {
                return rsa;
            }

            return null;
        }

        [InjectionMethod]
        public void Initialize()
        {
#if NET
            var keyVersion = 1;
            string key = null;
            do
            {
                key = ReadResourceFile(keyVersion);
                if (key != null)
                {
                    RSA rsa = RSA.Create();
                    rsa.ImportFromPem(key);
                    publicKeys.Add(keyVersion, rsa);
                }
                keyVersion++;
            } while (key != null);
#endif 
        }

        private string ReadResourceFile(int keyVersion)
        {
            string result;
            var filename = $"artifacts_public_key_{keyVersion.ToString().PadLeft(6, '0')}.pem";
            var assembly = Assembly.GetExecutingAssembly();
            var resourceName = $"Codeless.Server.Common.ArtifactsSigning.Keys.{filename}";

            using (Stream stream = assembly.GetManifestResourceStream(resourceName))
            {
                if (stream == null) return null;

                using (StreamReader reader = new StreamReader(stream))
                {
                    result = reader.ReadToEnd();
                }
            }

            return result;
        }

        #region Singleton

        public static IArtifactsPublicKeyProvider Instance { get => Nested.instance; }

        class Nested
        {
            protected Nested() { }
            static Nested() { }

            internal static readonly IArtifactsPublicKeyProvider instance = new ArtifactsPublicKeyProvider();
        }

        #endregion
    }
}

