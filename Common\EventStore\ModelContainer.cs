﻿using Codeless.Framework.EventProcessing.Event;
using Codeless.Framework.EventProcessing.Extensions;
using Codeless.Framework.EventProcessing.Models;
using Codeless.Framework.EventProcessing.MongoDB;
using Codeless.Framework.EventProcessing.MongoDB.DTO;
using Codeless.Framework.LockManagement;
using Codeless.Framework.Logging.Standard;
using Codeless.Server.Common.Cache.ClassStateCache;
using Codeless.Server.Common.ConnectionInformation;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;

namespace Codeless.Server.Common.EventStore
{
    public class ModelContainer : ModelContainerBase, IModelContainer
    {
        private static readonly StreamsModel model = new StreamsModel()
        {
            WorkerTypes = new List<Framework.EventProcessing.Configuration.WorkerType>(),
            StreamsByType = GetStreamsByType()
        };

        public static StreamsModel Model => model;
        private const string packageUpdaterSnapshotKey = "PackageUpdater.API";
        private readonly ILockManagerFactory lockManagerFactory;
        private readonly IConfigSettings configuration;
        private readonly IMongoDbClientFactory mongoDbClientFactory;

        public ModelContainer(IConfigSettings configuration, ILockManagerFactory lockManagerFactory, IDateTimeProvider dateTimeProvider, ILogger logger, ISnapshotStatisticsService snapshotStatisticsService, IMongoDbClientFactory mongoDbClientFactory)
            : base(configuration.EventStore, configuration.Environment, lockManagerFactory, dateTimeProvider, model, snapshotStatisticsService, logger)
        {
            this.lockManagerFactory = lockManagerFactory;
            this.configuration = configuration;
            this.mongoDbClientFactory = mongoDbClientFactory;
        }

        public event EventHandler StartLeaderWorkEvent;
        public event EventHandler StartManagerWorkEvent;
        public event EventHandler StopLeaderWorkEvent;
        public event EventHandler StopManagerWorkEvent;
        public event EventHandler ArtifactModelVersionUpdated;

        public void OnModelVersionUpdated()
        {
            ArtifactModelVersionUpdated?.Invoke(this, new EventArgs());
        }

        public void OnClassStateReceived(ClassStateModel classStateModel)
        {
            ClassStateCache.Instance.UpdateClassState(classStateModel);
        }

        public override void StartLeaderWork()
        {
            StartLeaderWorkEvent?.Invoke(this, new EventArgs());
        }

        public override void StopLeaderWork()
        {
            StopLeaderWorkEvent?.Invoke(this, new EventArgs());
        }

        public override void StartManagerWork()
        {
            StartManagerWorkEvent?.Invoke(this, new EventArgs());
        }

        public override void StopManagerWork()
        {
            StopManagerWorkEvent?.Invoke(this, new EventArgs());
        }

        public override bool SaveSnapshot()
        {
            return false;
        }

        public override bool ImportModelSnapshot(out List<StreamEventInfo> streamEventInfos)
        {
            bool imported = false;
            streamEventInfos = null;

            if (!configuration.EventStore.Enabled)
            {
                return imported;
            }

            try
            {
                Stopwatch stopwatch = logger.IsInfoEnabled ? Stopwatch.StartNew() : null;

                imported = ImportClassStateSnapshot(out streamEventInfos);

                if (logger.IsInfoEnabled)
                {
                    stopwatch?.Stop();
                    logger.WriteInfo($"Imported model snapshot from mongo within {stopwatch?.ElapsedMilliseconds} ms.");
                }
            }
            catch (Exception ex)
            {
                logger.WriteError("Error encountered while importing the model snapshot.", ex);
            }

            return imported;
        }

        protected override bool ShouldRemoveEventsFromModel(CollectionUpdatedEventArgs args)
        {
            return false;
        }

        protected override bool ShouldRemoveEventFromModel(CollectionUpdatedEventArgs args, object id)
        {
            return false;
        }

        protected override void RemoveEventFromModel(Enum eventType, object id)
        {
            // Do nothing because snapshot not implemented yet.
        }

        private bool ImportClassStateSnapshot(out List<StreamEventInfo> streamEventInfos)
        {
            var mongoDBConfiguration = CreateMongoDBConfiguration();
            var dataAccessFactory = CreateMongoDbDataAccessFactory(mongoDBConfiguration);
            var dataAccessSnapshot = dataAccessFactory.GetDataAccess<ModelSnapshot, string>();
            var dataAccessClassStates = dataAccessFactory.GetDataAccess<ClassStateModel, string>();
            var modelSnapshot = GetModelSnapshot(dataAccessSnapshot);

            if (modelSnapshot == null)
            {
                streamEventInfos = null;
                return false;
            }

            streamEventInfos = GetStreamEventInfos(modelSnapshot);

            dataAccessClassStates.AsQueryable().ForEach(OnClassStateReceived);

            return true;
        }

        private MongoDBConfiguration CreateMongoDBConfiguration()
        {
            return new MongoDBConfiguration(
                configuration.GetMongoDBConnection(configuration.ClassStateMongoDBConnection, configuration.ClassStateMongoDatabase),
                configuration.ClassStateMongoDatabase
            );
        }

        private MongoDbDataAccessFactory CreateMongoDbDataAccessFactory(MongoDBConfiguration mongoDBConfiguration)
        {
            return new MongoDbDataAccessFactory(lockManagerFactory, mongoDBConfiguration, mongoDbClientFactory);
        }

        private ModelSnapshot GetModelSnapshot(IMongoDBDataAccess<ModelSnapshot, string> dataAccessSnapshot)
        {
            return dataAccessSnapshot.AsQueryable().FirstOrDefault(m => m.Id == packageUpdaterSnapshotKey);
        }

        private List<StreamEventInfo> GetStreamEventInfos(ModelSnapshot modelSnapshot)
        {
            return modelSnapshot.Streams
                .Where(x => x.Name == EventStreamType.ClassState.ToString())
                .ToList();
        }
        private static Dictionary<EventStreamType, StreamType> GetStreamsByType()
        {
            var streamsByType = new Dictionary<EventStreamType, StreamType>()
            {
                { EventStreamType.Reporting, StreamType.Write },
                { EventStreamType.Profiler, StreamType.Write },
                { EventStreamType.Activity, StreamType.Write },
                { EventStreamType.TaskActivitySchedulers, StreamType.Write },
                { EventStreamType.HeartbeatInfo, StreamType.Write },
                { EventStreamType.TaskActivity, StreamType.Write },
                { EventStreamType.ServerHeartbeat, StreamType.ReadWrite },
                { EventStreamType.CallCancelationRequest, StreamType.Read },
            };
            if (ConfigSettings.Instance.UseEventStoreDataVersions)
            {
                streamsByType.Add(EventStreamType.DataVersions, StreamType.ReadWrite);
            }

            if (ConfigSettings.Instance.UseEventStoreCacheVersions)
            {
                streamsByType.Add(EventStreamType.CacheVersions, StreamType.ReadWrite);
            }

            if (ConfigSettings.Instance.ExpectedNumberOfBridges > 0)
            {
                streamsByType.Add(EventStreamType.BridgeHeartbeat, StreamType.Read);
            }

            if (ConfigSettings.Instance.EventStore.Enabled)
            {
                streamsByType.Add(EventStreamType.ClassState, StreamType.ReadWrite);
            }

            return streamsByType;
        }

    }
}
