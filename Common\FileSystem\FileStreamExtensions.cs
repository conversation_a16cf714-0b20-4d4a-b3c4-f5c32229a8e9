﻿using System.IO;

namespace Codeless.Server.Common
{
    public static class FileStreamExtensions
    {
        public static byte[] ReadAllBytes(this FileStream fs)
        {
            return ReadAllBytes((Stream)fs);
        }

        public static byte[] ReadAllBytes(this Stream stream, byte[] bytes = null, int? charCount = null)
        {
            byte[] buffer = (bytes != null) ? bytes : new byte[stream.Length];
            int readCharCount = (charCount.HasValue) ? charCount.Value : buffer.Length;
            
            int bytesRead = stream.Read(buffer, 0, readCharCount);
            if (bytesRead < readCharCount)
            {
                throw new EndOfStreamException("Fewer bytes were read from the stream than expected.");
            }
            return buffer;
        }
    }
}