﻿using Codeless.Framework.DependencyInjection;
using Codeless.Framework.Logging.Standard;
using Codeless.Framework.Sql.Standard;
using Codeless.Framework.LockManagement;
using Codeless.Server.Common.ConnectionInformation;
using Codeless.Server.Interfaces.Common.Cache;
using System;

namespace Codeless.Server.Common.Cache.CacheVersions
{
    public class CacheVersionsCache : CacheVersionsBase
    {
        private CacheVersionsCache()
        {
        }

        public override VersionInfo GetCacheVersions(bool forceReload = false)
        {
            VersionInfo result = null;

            EnsureCorrectCacheVersions(forceReload);

            lockManagerGlobal.LockReadMode(() =>
            {
                result = this.Versions;
            });

            return result;
        }

        public override int GetCacheVersion(CacheVersionTypes cacheVersionType, bool forceReload = false)
        {
            if (cacheVersionType == CacheVersionTypes.Metadata)
            {
                throw new ArgumentException("Use GetMetadata to obtain the metadata version.", "cacheVersionType");
            }

            EnsureCorrectCacheVersions(forceReload);
            int result = GetGlobalCacheVersion(cacheVersionType);

            return result;
        }

        public override void UpdateCacheVersion(CacheVersionTypes type, int? version, bool runtimeModelUpdate)
        {
            UpdateCacheVersionInDatabase(type);
            EnsureCorrectCacheVersions(true);
        }

        public override VersionInfo GetCacheVersionsPersonal(string userId, bool forceReload = false)
        {
            VersionInfo result = null;

            EnsureCorrectCacheVersionsPersonal(userId, forceReload);

            this.GetPersonalLockManager(userId).LockReadMode(() =>
            {
                this.UserVersions.TryGetValue(userId, out result);
            });

            return result;
        }

        public override int GetCacheVersionPersonal(CacheVersionTypes cacheVersionType, string userId, bool forceReload = false)
        {
            int result = 0;
            VersionInfo versionInfo = null;

            EnsureCorrectCacheVersionsPersonal(userId, forceReload);

            this.GetPersonalLockManager(userId).LockReadMode(() =>
            {
                if (this.UserVersions.TryGetValue(userId, out versionInfo) && versionInfo != null)
                {
                    result =  PersonalCacheversionsSearch[cacheVersionType].Invoke(versionInfo);
                }
            });

            return result;
        }

        public override void UpdateCacheVersionPersonal(string userId, CacheVersionTypes type, int? version, bool runtimeModelUpdate)
        {
            if (!string.IsNullOrWhiteSpace(userId))
            {
                UpdateCacheVersionPersonalInDatabase(userId, type);
                EnsureCorrectCacheVersionsPersonal(userId, true);
            }
        }

        [InjectionMethod]
        public void Initialize(ILoggerFactory loggerFactory, ILockManagerFactory lockManagerFactory, IPackageInfo packageInfo, IConfigSettings configSettings, ISqlWorkerFactory sqlWorkerFactory, ISqlConnectionManager sqlConnectionManager)
        {
            InitializeCache(loggerFactory, lockManagerFactory, packageInfo, configSettings, sqlWorkerFactory, sqlConnectionManager);
        }

        private void EnsureCorrectCacheVersionsPersonal(string userId, bool forceReload = false)
        {
            if (!string.IsNullOrWhiteSpace(userId))
            {
                bool loadNeeded = false;

                GetPersonalLockManager(userId).LockReadMode(() =>
                {
                    if (forceReload || !this.UserVersions.TryGetValue(userId, out VersionInfo currentVersion))
                    {
                        currentVersion = null;
                    }

                    loadNeeded = (forceReload || currentVersion == null || currentVersion.ElapsedSecondsSinceLoad > reloadInterval);
                });

                if (loadNeeded)
                {
                    this.GetPersonalLockManager(userId).LockWriteMode(() =>
                    {
                        this.UserVersions[userId] = GetVersionsPersonalFromDatabase(userId);
                    });
                }
            }
        }

        private void EnsureCorrectCacheVersions(bool forceReload = false)
        {
            bool needsUpdating = false;

            lockManagerGlobal.LockReadMode(() =>
            {
                needsUpdating = this.CacheVersionsNeedsUpdating(forceReload);
            });

            if (needsUpdating)
            {
                lockManagerGlobal.LockWriteMode(() =>
                {
                    if (this.CacheVersionsNeedsUpdating(forceReload))
                    {
                        GetCacheVersionsFromDatabase(this.Versions);
                    }
                });
            }
        }

        private bool CacheVersionsNeedsUpdating(bool forceReload)
        {
            return (forceReload || this.Versions.ElapsedSecondsSinceLoad > reloadInterval);
        }

        #region Singleton

        public static ICacheVersionsCache Instance { get => Nested.instance; }

        class Nested
        {
            protected Nested() { }
            static Nested() { }

            private static ICacheVersionsCache GetInstance()
            {
                if (ConfigSettings.Instance.UseEventStoreCacheVersions)
                {
                    return new CacheVersionsEventBased();
                }

                return new CacheVersionsCache();
            }

            internal static readonly ICacheVersionsCache instance = GetInstance();
        }

        #endregion
    }
}