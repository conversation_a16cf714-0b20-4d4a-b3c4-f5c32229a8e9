﻿using System;

namespace Codeless.Server.Common.EventStore
{
    public class EventsGenerationRequest
    {
        public Guid Id { get; set; } = Guid.NewGuid();
        public int Count { get; set; }
        public int IntervalMin { get; set; } = 10;
        public int IntervalMax { get; set; } = 100;
        public EventsGenerationEventType EventsType { get; set; }
        public bool Failed { get; set; }
    }
}