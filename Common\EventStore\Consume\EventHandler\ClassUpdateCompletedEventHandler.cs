﻿using Codeless.Framework.EventProcessing.DTO;
using Codeless.Framework.EventProcessing.Event;
using Codeless.Framework.EventProcessing.Event.Contracts.ClassState.Events;
using Codeless.Framework.EventProcessing.MongoDB.DTO;
using System;

namespace Codeless.Server.Common.EventStore.Consume.EventHandler
{
	internal class ClassUpdateCompletedEventHandler : EventHandlerTypedBase<UpdateCompleted>
	{
		public ClassUpdateCompletedEventHandler(ModelContainer modelContainer) : base(modelContainer)
		{
		}

		protected override void ProcessEvent(UpdateCompleted eventObject, DateTime eventTime, RelatedEvent relatedEvent)
		{
			ClassStateModel classStateModel = new ClassStateModel
			{
				ClassId = eventObject.ClassId,
				Concern = eventObject.ConcernCode,
				State = ClassState.Available,
				Step = string.Empty,
				Message = string.Empty
			};

			modelContainer.OnClassStateReceived(classStateModel);
		}
	}
}
