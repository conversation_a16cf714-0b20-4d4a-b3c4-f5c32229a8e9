﻿using Codeless.Server.Common.EventStore.DTO;
using System;
using System.Collections.Concurrent;

namespace Codeless.Server.Common.Cache.BridgeHeartbeats
{
    public sealed class BridgeHeartbeatsCache : IBridgeHeartbeatsCache
    {
        private readonly ConcurrentDictionary<string, BridgeInstanceHeartbeat> bridgeHeartbeats = new ConcurrentDictionary<string, BridgeInstanceHeartbeat>();

        public bool IsBridgeOnline(string connectionName)
        {
            DateTime utcNow = DateTime.UtcNow;
            int bridgeHeartbeatInterval = ConfigSettings.Instance.Environment.HeartbeatCheckIntervalSeconds;

            bool isOnline = bridgeHeartbeats.TryGetValue(connectionName, out BridgeInstanceHeartbeat heartbeat)
                            && heartbeat.BrdigeHeartbeatTime.AddSeconds(bridgeHeartbeatInterval) >= utcNow
                            && heartbeat.SourceServerHeartbeatInterval > 0
                            && heartbeat.SourceServerHeartbeatTime.HasValue
                            && heartbeat.SourceServerHeartbeatTime.Value.AddSeconds(bridgeHeartbeatInterval + heartbeat.SourceServerHeartbeatInterval.Value) >= utcNow;

            return isOnline;
        }

        public void UpdateBridgeHeartbeat(string connectionName, BridgeInstanceHeartbeat heartbeat)
        {
            bridgeHeartbeats.AddOrUpdate(connectionName, heartbeat, (key, oldValue) => heartbeat);
        }

        #region Singleton

        private static readonly Lazy<BridgeHeartbeatsCache> singleInstance = new Lazy<BridgeHeartbeatsCache>(() => new BridgeHeartbeatsCache());

        private BridgeHeartbeatsCache() { }

        public static IBridgeHeartbeatsCache Instance => singleInstance.Value;

        #endregion
    }
}
