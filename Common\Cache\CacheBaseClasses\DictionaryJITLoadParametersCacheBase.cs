﻿using System;
using System.Collections.Generic;

namespace Codeless.Server.Common.Cache.CacheBaseClasses
{
    /// <summary>
    /// Cache based on a dictionary of items are accessed by key and parameters and that are read just-in-time the first time the are needed.
    /// Loading is done sequentially, based on key and parameter values.
    public abstract class DictionaryJITLoadParametersCacheBase<TKey, TValue, TParameteter> : DictionaryJITLoadCacheBase<TKey, TValue>
    {
        protected abstract TValue LoadDataItem(TKey key, TParameteter parameter);
        protected virtual void EnsureCorrectDataLoaded(TParameteter parameteter)
        {
        }

        protected override bool TryGetCacheItem(TKey key, out TValue item)
        {
            throw new NotSupportedException("Use TryGetCacheItem(key, parameter, out item) instead.");
        }

        protected bool TryGetCacheItem(TKey key, TParameteter parameter, out TValue item)
        {
            item = GetCacheItem(key, parameter);
            return !EqualityComparer<TValue>.Default.Equals(item, default);
        }

        protected override TValue GetCacheItem(TKey key)
        {
            throw new NotSupportedException("Use GetCacheItem(key, parameter) instead.");
        }

        protected TValue GetCacheItem(TKey key, TParameteter parameter)
        {
            EnsureCorrectDataLoaded();
            EnsureCorrectDataLoaded(parameter);

            TValue item = default(TValue);
            bool inCache = false;
            this.lockManager.LockReadMode(() =>
            {
                inCache = this.items.TryGetValue(key, out item);
            });

            if (!inCache)
            {
                this.lockManager.LockUpgradeMode(() =>
                {
                    if (!this.items.TryGetValue(key, out item))
                    {
                        item = this.LoadDataItem(key, parameter);

#pragma warning disable S2955 // Generic parameters not constrained to reference types should not be compared to "null"
                        if (item != null)
                        {
                            this.lockManager.LockWriteMode(() =>
                            {
                                if (!items.ContainsKey(key))
                                {
                                    items.Add(key, item);
                                }
                            });
                        }
#pragma warning restore S2955 // Generic parameters not constrained to reference types should not be compared to "null"
                    }
                });
            }

            return item;
        }

        /// <summary>
        /// This method should no be used in the class. Call LoadDataItem(key, parameters) instead.
        /// </summary>
        /// <returns></returns>
        protected override TValue LoadDataItem(TKey key)
        {
            return default(TValue);
        }
    }
}