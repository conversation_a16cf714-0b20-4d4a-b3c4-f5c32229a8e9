﻿using Codeless.Framework.LockManagement;
using Codeless.Server.Common.Performance;

namespace Codeless.Server.Common.JsonConverters
{
    public static class RedisSerializer
    {
        private static readonly RedisSerializerService redisSerializerService = new RedisSerializerService(new LockManagerFactory(LockLogging.Instance), ConfigSettings.Instance);

        public static void RegisterDeserializeConverter<T>(T instance) where T : System.Text.Json.Serialization.JsonConverter
        {
            redisSerializerService.RegisterDeserializeConverter(instance);
        }

        public static string Serialize<T>(T objectToSerialize, bool useNewtonsoft = false) where T : class
        {
            return redisSerializerService.Serialize(objectToSerialize, useNewtonsoft);
        }

        public static T Deserialize<T>(string data, bool useNewtonsoft = false) where T : class, new()
        {
            return redisSerializerService.Deserialize<T>(data, useNewtonsoft);
        }
    }
}
