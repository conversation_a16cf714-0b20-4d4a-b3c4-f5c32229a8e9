﻿using Codeless.Framework.EventProcessing.DTO;
using Codeless.Framework.EventProcessing.Event;
using Codeless.Framework.EventProcessing.Event.Contracts.ClassState.Events;
using Codeless.Framework.EventProcessing.MongoDB.DTO;
using System;

namespace Codeless.Server.Common.EventStore.Consume.EventHandler
{
	public class ClassUpdateFailedEventHandler : EventHandlerTypedBase<UpdateFailed>
	{
		public ClassUpdateFailedEventHandler(ModelContainer modelContainer) : base(modelContainer)
		{
		}

		protected override void ProcessEvent(UpdateFailed eventObject, DateTime eventTime, RelatedEvent relatedEvent)
		{
			ClassStateModel classStateModel = new ClassStateModel
			{
				ClassId = eventObject.ClassId,
				Concern = eventObject.ConcernCode,
				State = ClassState.Error,
				Step = eventObject.Step,
				Message = eventObject.Message
			};

			modelContainer.OnClassStateReceived(classStateModel);
		}
	}
}
