﻿using System;
using System.IO;
using System.Security.Cryptography;

namespace Codeless.Server.Common.Helper
{
    public class AesEncrypter
    {

        private readonly string iv;
        private readonly string salt;
        private readonly int iterations;
        private readonly int keySize;
        private readonly int blockSize;

        public AesEncrypter(string iv, string salt, int blocksize, int keySize, int iterations)
        {
            this.iv = iv;
            this.salt = salt;
            this.iterations = iterations;
            this.blockSize = blockSize;
            this.keySize = keySize;
        }

        public AesEncrypter()
        {
            this.iv = ServerConstants.AesEncryptionIV;
            this.salt = ServerConstants.AesEncryptionSalt;
            this.iterations = ServerConstants.AesEncryptionIterations;
            this.blockSize = ServerConstants.AesEncryptionBlockSize;
            this.keySize = ServerConstants.AesEncryptionKeySize;
        }
        public string Encrypt(string text, string password)
        {
            if (string.IsNullOrEmpty(text))
                throw new ArgumentNullException(nameof(text));
            if (string.IsNullOrEmpty(password))
                throw new ArgumentNullException(nameof(password));
            byte[] Key = HexStringToByteArray(iv); 
            byte[] IV = GenerateKey(password, System.Text.Encoding.UTF8.GetBytes(salt));            
            if (Key is null || Key.Length <= 0)
                throw new ArgumentNullException(nameof(Key));
            if (IV is null || IV.Length <= 0)
                throw new ArgumentNullException(nameof(IV));
            byte[] encrypted;
            using (Aes aesAlg = Aes.Create())
            {
                aesAlg.Key = Key;
                aesAlg.IV = IV;
                ICryptoTransform encryptor = aesAlg.CreateEncryptor(aesAlg.Key, aesAlg.IV);
                using (MemoryStream msEncrypt = new MemoryStream())
                {
                    using (CryptoStream csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
                    {
                        using (StreamWriter swEncrypt = new StreamWriter(csEncrypt))
                        {
                            swEncrypt.Write(text);
                        }
                        encrypted = msEncrypt.ToArray();
                    }
                }
            }
            return Convert.ToBase64String(encrypted);
        }

        public static byte[] HexStringToByteArray(string strHex)
        {
            byte[] result = new byte[strHex.Length / 2];
            for (int i = 0; i <= strHex.Length - 1; i += 2)
            {
                result[i / 2] = Convert.ToByte(Convert.ToInt32(strHex.Substring(i, 2), 16));
            }
            return result;
        }

        private byte[] GenerateKey(string password, byte[] salt)
        {
            Rfc2898DeriveBytes rfc2898 = new Rfc2898DeriveBytes(System.Text.Encoding.UTF8.GetBytes(password), salt, iterations, HashAlgorithmName.SHA256);

            return rfc2898.GetBytes(128 / 8);
        }

        
    }
}
