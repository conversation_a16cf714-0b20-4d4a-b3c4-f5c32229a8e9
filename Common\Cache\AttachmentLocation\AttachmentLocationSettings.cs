﻿using Codeless.Framework.IO.Standard;
using Codeless.Framework.Logging.Standard;
using Codeless.Framework.Sql.Standard;
using Codeless.Server.Common.Cache.ClassPersistence;
using Codeless.Server.Common.ConnectionInformation;
using Codeless.Server.Common.Logging;
using Codeless.Server.Common.Sql;
using Codeless.Server.CommunicationClasses;
using Codeless.Server.MetaClasses;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text.RegularExpressions;

namespace Codeless.Server.Common.Cache.AttachmentLocation
{
    public static class AttachmentLocationSettings
    {
        private readonly static ILogger logger = LoggerFactorySingleton.Instance.GetLoggerFactory().GetLogger(typeof(AttachmentLocationSettings));
        private readonly static IFileSystem fileSystem = Storage.Instance.FileSystem;
        private readonly static ISqlWorkerFactory sqlWorkerFactory = new SqlWorkerFactory();
        private readonly static IConfigSettings configSettings = ConfigSettings.Instance;

        public static bool HasCustomAttachmentLocation(int classId)
        {
            return AttachmentLocationCache.Instance.GetCustomLocation(classId) != null;
        }

        public static bool HasCustomAttachmentLocation(int classId, out bool containsTokenAttributeIds, out bool isObjectDependent)
        {
            CustomAttachmentLocation customLocation = AttachmentLocationCache.Instance.GetCustomLocation(classId);
            bool hasCustomAttachmentLocation = customLocation != null;

            if (hasCustomAttachmentLocation)
            {
                containsTokenAttributeIds = customLocation.TokenAttributeIds.Count > 0;
                isObjectDependent = containsTokenAttributeIds || customLocation.AttachmentLocation.Contains("[ObjectId]");
            }
            else
            {
                containsTokenAttributeIds = false;
                isObjectDependent = false;
            }

            return hasCustomAttachmentLocation;
        }

        public static string AttachmentLocationDefault(int classId, long objectId, string concern, string company, bool ensureExists = false)
        {
            return EnsureExists(GetAttachmentLocationDefault(classId, objectId, concern), ensureExists);
        }

        public static string AttachmentLocation(int classId, long objectId, string concern, string company, bool ensureExists = false)
        {
            return EnsureExists(GetCustomAttachmentLocation(classId, objectId, concern, company, null, CustomLocationUndefinedBehavior.ReturnDefault), ensureExists);
        }

        public static string AttachmentLocationCustom(int classId, long objectId, string concern, string company, List<AttributeValue> objectValues, bool ensureExists = false)
        {
            return EnsureExists(GetCustomAttachmentLocation(classId, objectId, concern, company, objectValues, CustomLocationUndefinedBehavior.ThrowException), ensureExists);
        }

        public static string AttachmentLocationCustomPerClassId(int classId, string concern, string company, bool ensureExists = false)
        {
            return EnsureExists(GetCustomAttachmentLocationPerClassId(classId, concern, company), ensureExists);
        }

        public static string GetAttachmentLocationDefaultPerClass(int classId, string concern, bool ensureExists = false)
        {
            return EnsureExists(GetClassPath(classId, concern), ensureExists);
        }

        private static string GetAttachmentLocationDefault(int classId, long objectId, string concern)
        {
            return PathExt.Combine(GetClassPath(classId, concern), $"Object {objectId}");
        }

        private static string EnsureExists(string path, bool force)
        {
            if (force && !string.IsNullOrWhiteSpace(path))
            {
                fileSystem.CreateDirectory(path, CreateOption.IfNotExists);
            }

            return path;
        }

        /// <summary>
        /// returns path to store object attachments
        /// </summary>
        /// <param name="classId"></param>
        /// <param name="concern"></param>
        /// <returns>string</returns>
        private static string GetClassPath(int classId, string concern)
        {
            string path;
            string userFilesFolder = PackageInfo.Instance.GetDefaultPackage().ServerFiles.UserFilesFolder;
            string classIdFolder = $"Class {classId}";
            string concernAllFolder = "Concern All";
            string concernFolder = $"Concern {concern}";

            if (PackageInfo.Instance.GetDefaultPackage().StoreAttachmentsPerConcern)
            {
                PersistencyLevel persistence = ClassPersistenceCache.Instance.GetClassPersistence(classId);

                if (persistence == PersistencyLevel.System)
                {
                    path = PathExt.Combine(userFilesFolder, concernAllFolder, classIdFolder);
                }
                else
                {
                    path = PathExt.Combine(userFilesFolder, concernFolder, classIdFolder);
                }
            }
            else
            {
                path = PathExt.Combine(userFilesFolder, classIdFolder);
            }

            path = ReplaceTokens(path);

            return path;
        }

        private static string GetCustomAttachmentLocation(int classId, long objectId, string concern, string company, List<AttributeValue> objectValues, CustomLocationUndefinedBehavior behavior)
        {
            string filepath;

            CustomAttachmentLocation custom = AttachmentLocationCache.Instance.GetCustomLocation(classId);

            if (custom != null)
            {
                filepath = UpdateFilepath(concern, company, classId, objectId, custom.AttachmentLocation);

                if (!(custom.TokenAttributeIds?.Count > 0))
                {
                    return filepath;
                }

                bool checkTokens = true;

                if (objectValues?.Count > 0)
                {
                    filepath = ReplaceAttributeIds(objectValues, filepath);
                }
                else if (objectId > 0)
                {
                    List<AttributeValue> objectAttributesValues = GetAttributeIds(custom, classId, concern, objectId);

                    if (objectAttributesValues != null)
                    {
                        filepath = ReplaceAttributeIds(objectAttributesValues, filepath);
                    }
                }
                else
                {
                    checkTokens = false;
                }

                if (checkTokens)
                {
                    List<string> tokens = Regex.Matches(filepath, @"\[\d+\]", RegexOptions.None, TimeSpan.FromMilliseconds(configSettings.RegExTimeoutMs)).Cast<Match>().Select(m => m.Value).ToList();

                    if (tokens.Count > 0)
                    {
                        logger.WriteWarning(string.Format("Failed to replace token(s): " + string.Join(", ", tokens)));
                    }
                }
            }
            else
            {
                switch (behavior)
                {
                    case CustomLocationUndefinedBehavior.ReturnDefault:
                        filepath = GetAttachmentLocationDefault(classId, objectId, concern);
                        break;

                    default: // or behavior == CustomLocationUndefinedBehavior.ThrowException
                        throw new ArgumentException($"Custom attachment location was not defined for class {classId}");
                }
            }

            return filepath;
        }

        private static string UpdateFilepath(string concern, string company, int classId, long? objectId, string attachmentLocation)
        {
            string filepath = attachmentLocation;
            filepath = ReplaceTokens(filepath);
            string resolvedConcern = MetaClassesHelpers.ResolveDatabaseObjectNameConcern(concern);
            filepath = filepath.Replace("[Concern]", resolvedConcern);
            filepath = filepath.Replace("[Company]", company);
            filepath = filepath.Replace("[ClassId]", classId.ToString());

            if (objectId != null && objectId.Value > 0)
            {
                filepath = filepath.Replace("[ObjectId]", objectId.ToString());
            }

            return filepath;
        }

        private static string ReplaceTokens(string value)
        {
            value = TokenHelper.Instance.Replace(value, Enumerations.Token.ServerFiles);
            value = TokenHelper.Instance.Replace(value, Enumerations.Token.EnvironmentName);
            value = TokenHelper.Instance.Replace(value, Enumerations.Token.RootUrl);
            value = TokenHelper.Instance.Replace(value, Enumerations.Token.BaseUrlRegex);
            value = TokenHelper.Instance.Replace(value, Enumerations.Token.CustomConfig);

            return value;
        }

        // reason for disable: FROM {table} cannot be parameter
        private static List<AttributeValue> GetAttributeIds(CustomAttachmentLocation custom, int classId, string concern, long objectId)
        {
            List<AttributeValue> objectAttributesValues = new List<AttributeValue>();
            string underlyingClassConcern = ClassPersistenceCache.Instance.GetFixedConcernPersistency(classId, concern);

            string view = DatabaseNamesFormatter.View(classId, underlyingClassConcern);
            string columns = string.Join(", ", DatabaseNamesFormatter.ViewColumns(custom.TokenAttributeIds));
            string sqlGetObjects = $"SELECT {columns} FROM {view} WITH (NOLOCK) WHERE [OBJECT_ID] = @objectId";

            using (IDbConnection connection = SqlConnectionManagerSingleton.Instance.ConnectionManager.GetNewOpenedSqlConnection(underlyingClassConcern))
            {
                ISqlWorker worker = sqlWorkerFactory.GetSqlWorker(connection);
                using (IDataReader reader = worker.ExecuteReader(sqlGetObjects, new SqlParameter("@objectId", objectId)))
                {
                    while (reader.Read())
                    {
                        objectAttributesValues = GetDataRow(reader, custom.TokenAttributeIds);
                    }
                }
            }

            return objectAttributesValues;
        }

        private static string ReplaceAttributeIds(List<AttributeValue> objectValues, string filepath)
        {
            Dictionary<int, object> attributeValues = new Dictionary<int, object>();

            foreach (AttributeValue attribute in objectValues)
            {
                attributeValues.Add(attribute.AttributeID, attribute.Value);
            }

            MatchEvaluator evaluator = (match) =>
            {
                return Replace(match, attributeValues);
            };

            return Regex.Replace(filepath, @"\[\d+\]", evaluator, RegexOptions.None, TimeSpan.FromMilliseconds(configSettings.RegExTimeoutMs));
        }

        private static string GetCustomAttachmentLocationPerClassId(int classId, string concern, string company)
        {
            CustomAttachmentLocation custom = AttachmentLocationCache.Instance.GetCustomLocation(classId);
            string filepath = string.Empty;

            if (custom != null)
            {
                filepath = UpdateFilepath(concern, company, classId, null, custom.AttachmentLocation);
            }

            return filepath;
        }

        private static string Replace(Capture match, IDictionary<int, object> attValues)
        {
            int attributeId = int.Parse(match.Value.Replace("[", string.Empty).Replace("]", string.Empty));

            if (attValues.ContainsKey(attributeId))
            {
                if (attValues[attributeId] == null)
                {
                    return string.Empty;
                }
                else
                {
                    return attValues[attributeId].ToString();
                }
            }
            else
            {
                return match.Value;
            }
        }

        public static List<AttributeValue> GetDataRow(IDataReader reader, List<int> attributeIds)
        {
            List<AttributeValue> attributes = new List<AttributeValue>();

            for (int i = 0; i < reader.FieldCount; i++)
            {
                object value = reader.GetValue(i);
                if (value != null)
                {
                    if (value is string strValue)
                    {
                        attributes.Add(new AttributeValue { AttributeID = attributeIds[i], Value = strValue.Trim() });
                    }
                    else
                    {
                        attributes.Add(new AttributeValue { AttributeID = attributeIds[i], Value = value });
                    }
                }
            }

            return attributes;
        }
    }
}