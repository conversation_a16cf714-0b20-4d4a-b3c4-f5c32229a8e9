﻿using AutoMapper;
using Codeless.Framework.DependencyInjection;
using Codeless.Framework.EventProcessing;
using Codeless.Framework.EventProcessing.Event;
using Codeless.Framework.EventProcessing.Extensions;
using Codeless.Framework.EventProcessing.MongoDB;
using Codeless.Framework.LifeCycleManagement;
using Codeless.Framework.LockManagement;
using Codeless.Server.Common.EventStore.Consume;
using Codeless.Server.Common.Helper;
using Codeless.Server.Common.LifeCycle;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Codeless.Server.Common.EventStore
{
    public class EventStoreManagerSingleton : BackgroundWorkerBase, IEventStoreManagerSingleton
    {
        private IModelContainer modelContainer;
        private EventStoreManager eventStoreManager;
        private EventProcessor eventProcessor;

        public IEventStoreManager GetEventStoreManager() => eventStoreManager;
        public IModelContainer GetModelContainer() => modelContainer;
        public IEventProcessor GetEventProcessor() => eventProcessor;

        protected override string Name => nameof(EventStoreManagerSingleton);

        protected override void DoWork()
        {
            if (modelContainer.TryImportModelSnapshot(out List<StreamEventInfo> streamEventInfos))
            {
                eventStoreManager.Start(streamEventInfos);
            }
            else
            {
                eventStoreManager.Start();
            }
        }

        protected override List<Task> StopWork()
        {
            return eventStoreManager.Stop();
        }

        [InjectionMethod]
        public void Initialize(IConfigSettings configuration,
                              ILifeCycleManager<StartupStep, ShutdownStep> lifeCycleManager,
                              Codeless.Framework.Logging.Standard.ILoggerFactory loggerFactoryStandard,
                              IMapper mapper, 
                              ISnapshotStatisticsService snapshotStatisticsService,
                              IMongoDbClientFactory mongoDbClientFactory)
        {
            logger = loggerFactoryStandard.GetLogger(typeof(EventStoreManagerSingleton));
            IDateTimeProvider dateTimeProvider = new DateTimeProvider();
            
            var lockManagerFactory = new LockManagerFactory();
            modelContainer = new ModelContainer(configuration, lockManagerFactory, dateTimeProvider, loggerFactoryStandard.GetLogger(this.GetType()), snapshotStatisticsService, mongoDbClientFactory);
            
            eventProcessor = new EventProcessor(configuration.EventStore, mapper, loggerFactoryStandard, modelContainer as ModelContainer, snapshotStatisticsService);
            eventStoreManager = new EventStoreManager(configuration, eventProcessor, modelContainer as ModelContainer, lifeCycleManager, loggerFactoryStandard);
        }

        #region Singleton

        public static IEventStoreManagerSingleton Instance { get => Nested.instance; }

        class Nested
        {
            protected Nested() { }
            static Nested() { }

            internal static readonly IEventStoreManagerSingleton instance = new EventStoreManagerSingleton();
        }

        #endregion
    }
}
